/* Neo-brutalism 404 Page with Cute Elements */

/* Custom cursor with Safari fallback */
html, body, *, *::before, *::after {
    cursor: url('/images/cursor.png'), auto !important;
}

/* Ensure all interactive elements use the custom cursor */
button, a, input, select, textarea, .dressup-item, .game-cell, .character-container,
.dressup-tab, .dressup-button, .home-button, .dressup-random, .dressup-reset,
#closeDressUpButton, #resetButton, #playButton, #dressUpButton,
.dressup-items, .dressup-category, .dressup-options, .dressup-preview,
.dressup-content, .dressup-container, .error-container, .error-page {
    cursor: url('/images/cursor.png'), pointer !important;
}

/* Safari fallback for custom cursor */
@supports (-webkit-appearance: none) {
    html, body, *, *::before, *::after {
        cursor: auto !important;
    }

    button, a, input, select, textarea, .dressup-item, .game-cell, .character-container,
    .dressup-tab, .dressup-button, .home-button, .dressup-random, .dressup-reset,
    #closeDressUpButton, #resetButton, #playButton, #dressUpButton,
    .dressup-items, .dressup-category, .dressup-options, .dressup-preview,
    .dressup-content, .dressup-container, .error-container, .error-page {
        cursor: pointer !important;
    }
}

.error-page {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 40px 0;
    background-color: #fffef0;
    animation: gradientBackground 15s ease infinite;
    background-size: 400% 400%;
    background-image: linear-gradient(
        -45deg,
        #fffef0 0%,
        #ffecf1 25%,
        #f0f8ff 50%,
        #f5fff0 75%,
        #fffef0 100%
    );
    /* Performance optimizations */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    will-change: background-position;
}

.dark .error-page {
    background-color: #1a1a1a;
    animation: gradientBackground 15s ease infinite;
    background-size: 400% 400%;
    background-image: linear-gradient(
        -45deg,
        #1a1a1a 0%,
        #2a1a22 25%,
        #1a2a33 50%,
        #1a2a1a 75%,
        #1a1a1a 100%
    );
}

@keyframes gradientBackground {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Background pattern */
.error-page::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .error-page::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Main container */
.error-container {
    position: relative;
    z-index: 1;
    max-width: 800px;
    width: 90%;
    margin: 0 auto;
    padding: 40px 20px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    text-align: center;
    overflow: hidden;
}

.dark .error-container {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* 404 title */
.error-title {
    font-size: 8rem;
    font-weight: 800;
    color: #FF6B8B;
    margin-bottom: 0;
    line-height: 1;
    font-family: 'Playfair Display', serif;
    position: relative;
    display: inline-block;
}

.dark .error-title {
    color: rgba(255, 107, 139, 0.7);
}

/* Error message */
.error-message {
    font-size: 1.8rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
    font-family: 'Playfair Display', serif;
}

.dark .error-message {
    color: #fff;
}

/* Error description */
.error-description {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.dark .error-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Speech bubble */
.error-speech-bubble {
    position: relative;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 20px;
    padding: 10px 15px;
    max-width: 200px;
    margin: 0 auto 20px;
    text-align: center;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease forwards 1s;
}

.dark .error-speech-bubble {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.1);
    color: #fff;
}

.error-speech-bubble::after {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid #fff;
    z-index: 1;
}

.dark .error-speech-bubble::after {
    border-top-color: #2a2a2a;
}

.error-speech-bubble::before {
    content: '';
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 18px solid transparent;
    border-right: 18px solid transparent;
    border-top: 18px solid #000;
    z-index: 0;
}

.dark .error-speech-bubble::before {
    border-top-color: rgba(255, 255, 255, 0.7);
}

.error-speech-bubble p {
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
    color: #333;
}

.dark .error-speech-bubble p {
    color: rgba(255, 255, 255, 0.9);
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Buttons container */
.error-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* Home button */
.home-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #FFD1DC;
    color: #000;
    font-weight: 700;
    text-decoration: none;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    font-size: 1.1rem;
    cursor: pointer;
}

.dark .home-button {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.home-button:hover {
    transform: translate(-5px, -5px);
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .home-button:hover {
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.home-button:nth-child(2) {
    background-color: #A2D2FF;
}

.dark .home-button:nth-child(2) {
    background-color: rgba(162, 210, 255, 0.3);
}

.home-button:nth-child(3) {
    background-color: #B5EAD7;
}

.dark .home-button:nth-child(3) {
    background-color: rgba(181, 234, 215, 0.3);
}

/* Interactive elements - Shreya character */
.error-character {
    width: 200px;
    height: 220px;
    margin: 0 auto 30px;
    position: relative;
    cursor: pointer;
}

.character-container {
    width: 100%;
    height: 100%;
    position: relative;
    transition: transform 0.3s ease;
    /* Performance optimizations */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    will-change: transform;
}

.character-container:hover {
    transform: scale(1.05) translateZ(0);
}

/* Mobile optimization for character */
@media (max-width: 768px) {
    .character-container {
        transition: transform 0.2s ease;
    }

    .character-container:hover {
        transform: scale(1.02) translateZ(0);
    }
}

/* Shreya's head */
.character-head {
    width: 100px;
    height: 100px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    overflow: visible;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.dark .character-head {
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
}

/* Shreya's hair */
.character-head::before {
    content: "";
    position: absolute;
    top: -10px;
    left: -10px;
    width: 120px;
    height: 60px;
    background-color: #8B4513;
    border: 3px solid #000;
    border-radius: 50% 50% 0 0;
    z-index: 1;
}

.dark .character-head::before {
    background-color: #8B4513;
    border: 3px solid #000;
}

/* Shreya's bangs */
.character-head::after {
    content: "";
    position: absolute;
    top: 5px;
    left: 20px;
    width: 60px;
    height: 12px;
    background-color: #8B4513;
    border-bottom: 3px solid #000;
    border-radius: 50% 50% 40% 40%;
    z-index: 2;
}

.dark .character-head::after {
    background-color: #8B4513;
    border-bottom: 3px solid #000;
}

/* Shreya's pigtails */
.character-pigtail {
    position: absolute;
    width: 25px;
    height: 70px;
    background-color: #8B4513;
    border: 3px solid #000;
    border-radius: 15px;
    z-index: -1;
    top: 15px;
}

.character-pigtail.left {
    left: -15px;
    transform: rotate(-15deg);
}

.character-pigtail.right {
    right: -15px;
    transform: rotate(15deg);
}

.dark .character-pigtail {
    background-color: #8B4513;
    border: 3px solid #000;
}

/* Shreya's face */
.character-face {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 4;
}

/* Shreya's eyes */
.character-eyes {
    position: absolute;
    top: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
    gap: 30px;
    z-index: 5;
}

.character-eye {
    width: 18px;
    height: 18px;
    background-color: #000;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.dark .character-eye {
    background-color: #000;
}

/* Shreya's eye shine */
.character-eye::after {
    content: "";
    position: absolute;
    top: 3px;
    left: 3px;
    width: 6px;
    height: 6px;
    background-color: #fff;
    border-radius: 50%;
    z-index: 6;
}

.dark .character-eye::after {
    background-color: rgba(255, 255, 255, 0.8);
}

/* Shreya's eyelashes */
.character-eye.left::before {
    content: "";
    position: absolute;
    top: -5px;
    left: 2px;
    width: 12px;
    height: 3px;
    border-top: 2px solid #000;
    border-radius: 50%;
    z-index: 6;
}

.character-eye.right::before {
    content: "";
    position: absolute;
    top: -5px;
    right: 2px;
    width: 12px;
    height: 3px;
    border-top: 2px solid #000;
    border-radius: 50%;
    z-index: 6;
}

.dark .character-eye.left::before,
.dark .character-eye.right::before {
    border-top: 2px solid #000;
}

/* Shreya's mouth */
.character-mouth {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 10px;
    border-bottom: 3px solid #000;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.dark .character-mouth {
    border-bottom: 3px solid #000;
}

/* Shreya's blush */
.character-blush {
    position: absolute;
    width: 15px;
    height: 8px;
    background-color: #FF6B8B;
    border-radius: 50%;
    opacity: 0.6;
    bottom: 35px;
}

.character-blush.left {
    left: 15px;
}

.character-blush.right {
    right: 15px;
}

.dark .character-blush {
    background-color: #FF6B8B;
    opacity: 0.6;
}

/* Shreya's dress */
.character-body {
    width: 120px;
    height: 100px;
    background-color: #A2D2FF;
    border: 3px solid #000;
    border-radius: 20px 20px 60px 60px;
    position: absolute;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.dark .character-body {
    background-color: #A2D2FF;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
}

/* Shreya's dress pattern */
.character-body::after {
    content: "";
    position: absolute;
    bottom: 10px;
    left: 0;
    width: 100%;
    height: 20px;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 10px,
        #FFD1DC 10px,
        #FFD1DC 20px
    );
}

.dark .character-body::after {
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 10px,
        #FFD1DC 10px,
        #FFD1DC 20px
    );
}

/* Shreya's arms */
.character-arm {
    width: 20px;
    height: 60px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    position: absolute;
    top: 90px;
    border-radius: 10px;
    z-index: 0;
    transition: all 0.3s ease;
}

.dark .character-arm {
    background-color: #FFD1DC;
    border: 3px solid #000;
}

.character-arm.left {
    left: 30px;
    transform: rotate(20deg);
    transform-origin: top center;
}

.character-arm.right {
    right: 30px;
    transform: rotate(-20deg);
    transform-origin: top center;
}

/* Shreya's legs */
.character-leg {
    width: 25px;
    height: 40px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    position: absolute;
    top: 170px;
    border-radius: 10px;
    z-index: 0;
    transition: all 0.3s ease;
}

.dark .character-leg {
    background-color: #FFD1DC;
    border: 3px solid #000;
}

.character-leg.left {
    left: 70px;
}

.character-leg.right {
    right: 70px;
}

/* Shreya's shoes */
.character-leg::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: -5px;
    width: 35px;
    height: 10px;
    background-color: #FF6B8B;
    border: 3px solid #000;
    border-radius: 10px;
}

.dark .character-leg::after {
    background-color: #FF6B8B;
    border: 3px solid #000;
}

/* Shreya's expressions */
.character-container.sad .character-mouth {
    border-bottom: none;
    border-top: 3px solid #000;
    bottom: 30px;
    top: auto;
}

.dark .character-container.sad .character-mouth {
    border-top: 3px solid #000;
}

.character-container.sad .character-eye {
    transform: rotate(10deg);
}

.character-container.surprised .character-eye {
    height: 20px;
    width: 20px;
    border-radius: 50%;
}

.character-container.surprised .character-mouth {
    width: 20px;
    height: 20px;
    border: 3px solid #000;
    border-radius: 50%;
    bottom: 20px;
}

.dark .character-container.surprised .character-mouth {
    border: 3px solid #000;
}

.character-container.happy .character-mouth {
    width: 40px;
    height: 20px;
}

.character-container.happy .character-eye {
    height: 5px;
    border-radius: 50%;
    background-color: transparent;
    border-bottom: 3px solid #000;
}

.dark .character-container.happy .character-eye {
    border-bottom: 3px solid #000;
    background-color: transparent;
}

.character-container.happy .character-eye::before {
    display: none;
}

.character-container.waving .character-arm.right {
    transform: rotate(-60deg);
    animation: waving 0.5s ease-in-out infinite alternate;
}

@keyframes waving {
    0% {
        transform: rotate(-60deg);
    }
    100% {
        transform: rotate(-30deg);
    }
}

/* New expressions */
.character-container.excited .character-mouth {
    width: 40px;
    height: 25px;
    border-radius: 0 0 20px 20px;
    border: none;
    border-bottom: 3px solid #000;
    bottom: 25px;
}

.character-container.excited .character-eye {
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background-color: #000;
    border: none;
}

.character-container.excited .character-eye::before {
    display: none;
}

.character-container.excited .character-blush {
    opacity: 1;
}

.character-container.excited .character-arm {
    animation: excited-arms 0.5s infinite alternate;
}

@keyframes excited-arms {
    0% {
        transform: rotate(-10deg);
    }
    100% {
        transform: rotate(10deg);
    }
}

.character-container.sleepy .character-mouth {
    width: 20px;
    height: 10px;
    border: none;
    border-bottom: 3px solid #000;
    bottom: 25px;
}

.character-container.sleepy .character-eye {
    height: 2px;
    background-color: transparent;
    border-bottom: 3px solid #000;
}

.character-container.sleepy .character-eye::before {
    display: none;
}

.character-container.angry .character-mouth {
    width: 30px;
    height: 10px;
    border: none;
    border-top: 3px solid #000;
    bottom: 25px;
    top: auto;
}

.character-container.angry .character-eye.left {
    transform: rotate(-20deg);
    height: 5px;
    background-color: transparent;
    border-bottom: 3px solid #000;
}

.character-container.angry .character-eye.right {
    transform: rotate(20deg);
    height: 5px;
    background-color: transparent;
    border-bottom: 3px solid #000;
}

.character-container.angry .character-eye::before {
    display: none;
}

.character-container.angry .character-blush {
    background-color: rgba(255, 0, 0, 0.3);
    opacity: 1;
}

.character-container.silly .character-mouth {
    width: 30px;
    height: 20px;
    border: none;
    border-bottom: 3px solid #000;
    border-radius: 0 0 10px 10px;
    bottom: 25px;
    transform: rotate(10deg);
}

.character-container.silly .character-eye.left {
    transform: rotate(-20deg) scale(1.2);
}

.character-container.silly .character-eye.right {
    transform: rotate(20deg) scale(0.8);
}

/* Floating items */
.floating-item {
    position: absolute;
    z-index: 0;
    animation: float 6s ease-in-out infinite;
    opacity: 0.7;
}

.floating-item.item1 {
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-item.item2 {
    top: 20%;
    right: 10%;
    animation-delay: 1s;
}

.floating-item.item3 {
    bottom: 15%;
    left: 15%;
    animation-delay: 2s;
}

.floating-item.item4 {
    bottom: 10%;
    right: 15%;
    animation-delay: 3s;
}

.floating-item.item5 {
    top: 40%;
    left: 5%;
    animation-delay: 1.5s;
}

.floating-item.item6 {
    top: 15%;
    left: 30%;
    animation-delay: 2.5s;
    font-size: 2.5rem;
}

.floating-item.item7 {
    bottom: 30%;
    right: 5%;
    animation-delay: 3.5s;
    font-size: 2rem;
}

.floating-item.item8 {
    bottom: 40%;
    right: 25%;
    animation-delay: 4s;
    font-size: 1.5rem;
}

.floating-item.heart {
    color: #FF6B8B;
    font-size: 2rem;
}

.dark .floating-item.heart {
    color: rgba(255, 107, 139, 0.7);
}

.floating-item.star {
    color: #FFD166;
    font-size: 2rem;
}

.dark .floating-item.star {
    color: rgba(255, 209, 102, 0.7);
}

.floating-item.circle {
    width: 30px;
    height: 30px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 50%;
}

.dark .floating-item.circle {
    background-color: rgba(162, 210, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.floating-item.square {
    width: 25px;
    height: 25px;
    background-color: #FFD1DC;
    border: 2px solid #000;
}

.dark .floating-item.square {
    background-color: rgba(255, 209, 220, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.floating-item.triangle {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 30px solid #B5EAD7;
    position: relative;
}

.floating-item.triangle::after {
    content: '';
    position: absolute;
    top: 0;
    left: -15px;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 30px solid #B5EAD7;
    border: 2px solid #000;
    border-top: none;
}

.dark .floating-item.triangle {
    border-bottom-color: rgba(181, 234, 215, 0.5);
}

.dark .floating-item.triangle::after {
    border-bottom-color: rgba(181, 234, 215, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
    border-top: none;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(10deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

/* Game containers */
.game-container {
    margin-top: 30px;
    padding: 20px;
    background-color: #f8f8f8;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    display: none;
}

.dark .game-container {
    background-color: #333;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Dress up game styles - Neo Brutalism Theme */
.dressup-container {
    margin-top: 30px;
    padding: 0;
    background-color: #f8f8f8;
    border: 4px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    max-width: 900px;
    width: 95%;
    margin-left: auto;
    margin-right: auto;
    display: none;
    overflow: hidden;
    position: relative;
}

.dark .dressup-container {
    background-color: #333;
    border: 4px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

.dressup-header {
    background-color: #FF6B8B;
    padding: 15px 20px;
    border-bottom: 4px solid #000;
    position: relative;
    z-index: 10;
}

.dark .dressup-header {
    background-color: rgba(255, 107, 139, 0.7);
    border-bottom: 4px solid rgba(255, 255, 255, 0.7);
}

.dressup-title {
    font-size: 1.8rem;
    font-weight: 800;
    color: #000;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.2);
}

.dark .dressup-title {
    color: #fff;
    text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.5);
}

.dressup-description {
    font-size: 1rem;
    color: #000;
    margin: 5px 0 0;
    font-weight: 500;
}

.dark .dressup-description {
    color: rgba(255, 255, 255, 0.9);
}

.dressup-content {
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

.dressup-options-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: 600px;
    width: 100%;
}

.dressup-tabs {
    display: flex;
    border-bottom: 4px solid #000;
    background-color: #A2D2FF;
}

.dark .dressup-tabs {
    border-bottom: 4px solid rgba(255, 255, 255, 0.7);
    background-color: rgba(162, 210, 255, 0.5);
}

.dressup-tab {
    padding: 12px 20px;
    font-weight: 700;
    font-size: 1rem;
    cursor: pointer;
    background-color: transparent;
    border: none;
    border-right: 4px solid #000;
    color: #000;
    position: relative;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dark .dressup-tab {
    color: #fff;
    border-right: 4px solid rgba(255, 255, 255, 0.7);
}

.dressup-tab:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.dark .dressup-tab:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dressup-tab.active {
    background-color: #FFD1DC;
    color: #000;
    font-weight: 800;
}

.dark .dressup-tab.active {
    background-color: rgba(255, 209, 220, 0.5);
}

.dressup-tab-content {
    display: none;
    padding: 20px;
    overflow-y: auto;
    max-height: 500px;
}

.dressup-tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.dressup-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.dressup-category {
    margin-bottom: 15px;
}

.dressup-category h4 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 3px solid #000;
    position: sticky;
    top: 0;
    background-color: #f8f8f8;
    padding-top: 5px;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dark .dressup-category h4 {
    color: #fff;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
}

.dressup-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start;
}

.dressup-item {
    width: 70px;
    height: 70px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    padding: 5px;
    position: relative;
    overflow: hidden;
}

.dark .dressup-item {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.1);
}

.dressup-item:hover {
    transform: translate(-3px, -3px);
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.3);
}

.dark .dressup-item:hover {
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.1);
}

.dressup-item.active {
    border: 3px solid #FF6B8B;
    box-shadow: 0 0 15px #FF6B8B;
    background-color: #FFE6E8;
    transform: translate(-3px, -3px);
}

.dark .dressup-item.active {
    border: 3px solid rgba(255, 107, 139, 0.7);
    box-shadow: 0 0 15px rgba(255, 107, 139, 0.7);
    background-color: rgba(255, 230, 232, 0.2);
}

.dressup-item::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 50%);
    pointer-events: none;
}

.color-swatch {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid #000;
    margin-bottom: 5px;
    transition: transform 0.2s ease;
}

.dark .color-swatch {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.dressup-item:hover .color-swatch {
    transform: scale(1.1);
}

.accessory-icon {
    font-size: 2rem;
    margin-bottom: 5px;
    transition: transform 0.2s ease;
}

.dressup-item:hover .accessory-icon {
    transform: scale(1.1);
}

.item-label {
    font-size: 0.8rem;
    text-align: center;
    font-weight: 700;
    color: #333;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.dark .item-label {
    color: rgba(255, 255, 255, 0.9);
}

.dressup-footer {
    padding: 15px;
    border-top: 4px solid #000;
    display: flex;
    justify-content: space-between;
    background-color: #B5EAD7;
}

.dark .dressup-footer {
    border-top: 4px solid rgba(255, 255, 255, 0.7);
    background-color: rgba(181, 234, 215, 0.3);
}

.dressup-button {
    padding: 10px 20px;
    background-color: #FFD1DC;
    color: #000;
    font-weight: 700;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.2s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.dark .dressup-button {
    background-color: rgba(255, 209, 220, 0.5);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.1);
    color: #fff;
}

.dressup-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.3);
}

.dark .dressup-button:hover {
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.1);
}

.dressup-random {
    background-color: #A2D2FF;
}

.dark .dressup-random {
    background-color: rgba(162, 210, 255, 0.5);
}

.dressup-reset {
    background-color: #FFD166;
}

.dark .dressup-reset {
    background-color: rgba(255, 209, 102, 0.5);
}

/* Confetti animation for random button */
.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #FFD1DC;
    animation: confetti-fall 3s ease-in-out forwards;
    z-index: 100;
}

.confetti:nth-child(2n) {
    background-color: #A2D2FF;
}

.confetti:nth-child(3n) {
    background-color: #B5EAD7;
}

.confetti:nth-child(4n) {
    background-color: #FFD166;
}

@keyframes confetti-fall {
    0% {
        transform: translateY(-50px) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(500px) rotate(360deg);
        opacity: 0;
    }
}

.game-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 15px;
}

.dark .game-title {
    color: #fff;
}

.game-description {
    font-size: 1rem;
    color: #555;
    margin-bottom: 20px;
}

.dark .game-description {
    color: rgba(255, 255, 255, 0.7);
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.game-cell {
    width: 70px;
    height: 70px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 auto;
    position: relative;
}

.dark .game-cell {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.game-cell:hover {
    transform: scale(1.05);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
}

.dark .game-cell:hover {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
}

/* Boy face (X) */
.game-cell.x::before {
    content: "";
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 50%;
    z-index: 1;
}

.dark .game-cell.x::before {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Boy hair */
.game-cell.x::after {
    content: "";
    position: absolute;
    width: 40px;
    height: 20px;
    top: 10px;
    background-color: #8B4513;
    border: 2px solid #000;
    border-radius: 50% 50% 0 0;
    z-index: 0;
}

.dark .game-cell.x::after {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Girl face (O) */
.game-cell.o::before {
    content: "";
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 50%;
    z-index: 1;
}

.dark .game-cell.o::before {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Girl hair */
.game-cell.o::after {
    content: "";
    position: absolute;
    width: 50px;
    height: 25px;
    top: 8px;
    background-color: #FF69B4;
    border: 2px solid #000;
    border-radius: 50% 50% 0 0;
    z-index: 0;
}

.dark .game-cell.o::after {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.game-status {
    font-size: 1.2rem;
    font-weight: 600;
    color: #000;
    margin-bottom: 15px;
}

.dark .game-status {
    color: #fff;
}

.game-button {
    display: inline-block;
    padding: 8px 15px;
    background-color: #FFD1DC;
    color: #000;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    cursor: pointer;
}

.dark .game-button {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.game-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .game-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Responsive design */
/* Fix for mobile item labels */
.dressup-item {
    position: relative;
}

.item-label {
    position: absolute;
    bottom: 2px;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 2px;
    border-top: 1px solid #ddd;
}

.dark .item-label {
    background-color: rgba(0, 0, 0, 0.7);
    border-top: 1px solid #444;
}

@media (max-width: 768px) {
    /* Improve 404 page layout */
    .error-page {
        padding: 20px 10px;
    }

    .error-container {
        width: 95%;
        padding: 30px 20px;
        box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .error-container {
        box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    }

    .error-title {
        font-size: 5rem;
        margin-bottom: 15px;
    }

    .error-message {
        font-size: 1.5rem;
        margin-bottom: 15px;
    }

    .error-description {
        font-size: 1rem;
        margin-bottom: 20px;
    }

    .error-buttons {
        gap: 10px;
    }

    .home-button {
        margin: 5px;
    }

    /* Responsive character */
    .error-character {
        width: 180px;
        height: 180px;
        margin: 0 auto 20px;
    }

    .character-head {
        width: 85px;
        height: 85px;
    }

    /* Shreya's hair adjustments */
    .character-head::before {
        width: 110px;
        height: 50px;
        top: -8px;
        left: -8px;
    }

    .character-head::after {
        width: 55px;
        height: 10px;
        top: 8px;
        left: 15px;
    }

    .character-pigtail {
        width: 22px;
        height: 65px;
        top: 12px;
    }

    .character-pigtail.left {
        left: -12px;
    }

    .character-pigtail.right {
        right: -12px;
    }

    .character-eyes {
        top: 35px;
        gap: 25px;
    }

    .character-eye {
        width: 15px;
        height: 15px;
    }

    .character-mouth {
        bottom: 22px;
        width: 25px;
    }

    .character-body {
        width: 100px;
        height: 80px;
        top: 70px;
    }

    .character-arm {
        width: 18px;
        height: 50px;
        top: 80px;
    }

    .character-arm.left {
        left: 25px;
    }

    .character-arm.right {
        right: 25px;
    }

    .character-leg {
        width: 20px;
        height: 35px;
        top: 150px !important; /* Force consistent positioning */
    }

    .character-leg.left {
        left: 50px;
    }

    .character-leg.right {
        right: 50px;
    }

    /* Speech bubble adjustments */
    .error-speech-bubble {
        max-width: 180px;
        padding: 10px 15px;
        margin-bottom: 20px;
    }

    .error-speech-bubble p {
        font-size: 0.9rem;
    }

    .game-cell {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    /* Responsive dress-up game */
    .dressup-container {
        max-width: 95%;
    }

    .dressup-content {
        flex-direction: column;
        min-height: auto;
    }

    .dressup-preview {
        border-right: none;
        border-bottom: 4px solid #000;
        padding: 15px;
    }

    .dark .dressup-preview {
        border-bottom: 4px solid rgba(255, 255, 255, 0.7);
    }

    .dressup-character-container {
        width: 200px;
        height: 250px;
        transform: scale(1);
    }

    .dressup-character-container:hover {
        transform: scale(1.05);
    }

    .dressup-tabs {
        overflow-x: auto;
        flex-wrap: nowrap;
    }

    .dressup-tab {
        padding: 10px 15px;
        font-size: 0.9rem;
        white-space: nowrap;
    }

    .dressup-tab-content {
        padding: 15px;
        max-height: 300px;
    }

    .dressup-options {
        max-height: 300px;
    }

    .dressup-item {
        width: 60px;
        height: 60px;
    }

    .item-label {
        font-size: 0.7rem;
    }

    .accessory-icon {
        font-size: 1.5rem;
    }

    .color-swatch {
        width: 30px;
        height: 30px;
    }

    .dressup-footer {
        padding: 10px;
    }

    .dressup-button {
        padding: 8px 15px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    /* Improve 404 page layout */
    .error-page {
        padding: 10px 5px;
    }

    .error-container {
        width: 98%;
        padding: 20px 10px;
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .error-container {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }

    .error-title {
        font-size: 3.5rem;
        margin-bottom: 10px;
    }

    .error-message {
        font-size: 1.2rem;
        margin-bottom: 10px;
    }

    .error-description {
        font-size: 0.9rem;
        margin-bottom: 15px;
    }

    .home-button {
        padding: 8px 15px;
        font-size: 0.9rem;
        margin: 5px;
    }

    .error-buttons {
        gap: 5px;
    }

    .game-cell {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    /* Responsive character */
    .error-character {
        width: 160px;
        height: 180px;
        margin: 0 auto 15px;
    }

    /* Shreya's head */
    .character-head {
        width: 80px;
        height: 80px;
    }

    /* Shreya's hair adjustments */
    .character-head::before {
        width: 90px;
        height: 40px;
        top: -5px;
        left: -5px;
        z-index: 1;
    }

    .character-head::after {
        width: 45px;
        height: 8px;
        top: 10px;
        left: 18px;
        z-index: 2;
    }

    .character-pigtail {
        width: 18px;
        height: 50px;
        z-index: 1;
    }

    .character-pigtail.left {
        left: -8px;
    }

    .character-pigtail.right {
        right: -8px;
    }

    /* Fix eyes position */
    .character-eyes {
        top: 30px;
        z-index: 10;
        gap: 22px;
    }

    /* Fix blush position */
    .character-blush {
        width: 12px;
        height: 6px;
        bottom: 30px;
    }

    .character-blush.left {
        left: 12px;
    }

    .character-blush.right {
        right: 12px;
    }

    /* Shreya's body */
    .character-body {
        width: 90px;
        height: 70px;
        top: 65px;
    }

    .character-arm {
        width: 15px;
        height: 45px;
        top: 70px;
    }

    .character-arm.left {
        left: 20px;
    }

    .character-arm.right {
        right: 20px;
    }

    .character-leg {
        width: 18px;
        height: 30px;
        top: 150px !important; /* Force consistent positioning */
    }

    .character-leg.left {
        left: 45px;
    }

    .character-leg.right {
        right: 45px;
    }

    /* Responsive dress-up game */
    .dressup-container {
        max-width: 100%;
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .dressup-container {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }

    .dressup-header {
        padding: 10px 15px;
    }

    .dressup-title {
        font-size: 1.4rem;
    }

    .dressup-description {
        font-size: 0.8rem;
    }

    .dressup-character-container {
        width: 160px;
        height: 200px;
    }

    .dressup-tab {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .dressup-tab-content {
        padding: 10px;
        max-height: 250px;
    }

    .dressup-category h4 {
        font-size: 1rem;
        padding-bottom: 5px;
        margin-bottom: 10px;
    }

    .dressup-items {
        gap: 8px;
    }

    .dressup-item {
        width: 45px;
        height: 45px;
        margin: 2px;
        border: 2px solid #000;
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
    }

    .dark .dressup-item {
        border: 2px solid rgba(255, 255, 255, 0.7);
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
    }

    .dressup-item:hover {
        transform: translate(-2px, -2px);
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.3);
    }

    .dark .dressup-item:hover {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.1);
    }

    .item-label {
        font-size: 0.65rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
        padding: 0 2px;
    }

    .accessory-icon {
        font-size: 1.2rem;
    }

    .color-swatch {
        width: 25px;
        height: 25px;
    }

    .dressup-footer {
        padding: 8px;
    }

    .dressup-button {
        padding: 6px 12px;
        font-size: 0.8rem;
        border: 2px solid #000;
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
    }

    .dark .dressup-button {
        border: 2px solid rgba(255, 255, 255, 0.7);
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
    }

    /* Adjust game board */
    .game-board {
        grid-gap: 5px;
    }

    /* Adjust boy/girl faces in tic-tac-toe */
    .game-cell.x::before,
    .game-cell.o::before {
        width: 30px;
        height: 30px;
    }

    .game-cell.x::after,
    .game-cell.o::after {
        width: 20px;
        top: 20px;
    }

    .game-cell.o .blush {
        width: 5px;
        height: 3px;
    }

    .game-cell.o .blush.left {
        left: 15px;
        top: 22px;
    }

    .game-cell.o .blush.right {
        right: 15px;
        top: 22px;
    }

    /* Speech bubble adjustments */
    .error-speech-bubble {
        max-width: 150px;
        padding: 8px 10px;
        margin-bottom: 15px;
    }

    .error-speech-bubble p {
        font-size: 0.8rem;
    }
}

/* Confetti styles */
#confettiContainer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
    overflow: hidden;
}

.confetti {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #FFD1DC;
    top: -10px;
    animation: confettiFall 3s linear forwards;
}

.confetti:nth-child(2n) {
    background-color: #A2D2FF;
}

.confetti:nth-child(3n) {
    background-color: #B5EAD7;
}

.confetti:nth-child(4n) {
    background-color: #FDFD96;
}

.confetti:nth-child(5n) {
    background-color: #C7CEEA;
}

@keyframes confettiFall {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(1000px) rotate(720deg);
        opacity: 0;
    }
}