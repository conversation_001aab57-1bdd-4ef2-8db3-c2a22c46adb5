{{ if .Site.Params.certifications.enable | default false }}
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/certifications-neo-cute.css">
<section id="certifications">
    <div class="container">
        <h3 class="text-center">{{ .Site.Params.certifications.title | default "Certifications" }}</h3>
        <div class="row justify-content-center py-5">
            {{ range .Site.Params.certifications.items }}
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                <div class="card h-100">
                    <div class="card-img-container">
                        {{ if .image }}
                        <img src="{{ .image }}" class="card-img" alt="{{ .title }}">
                        {{ end }}
                        {{ if .date }}
                        <div class="cert-date">{{ .date }}</div>
                        {{ end }}
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ .title }}</h5>
                        {{ if .issuer }}
                        <div class="cert-issuer">{{ .issuer }}</div>
                        {{ end }}
                        <p class="card-text">{{ .content }}</p>
                        {{ if .url }}
                        <a href="{{ .url }}" class="btn" target="_blank">View Certificate</a>
                        {{ end }}
                    </div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>
</section>
{{ end }}
