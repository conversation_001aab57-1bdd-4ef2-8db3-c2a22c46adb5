/* Neo-brutalism Landing Page Styles with Cute Elements */

/* Hero section */
.hero {
    position: relative;
    padding: 80px 0;
    overflow: hidden;
    background-color: #fffef0;
    border-bottom: 3px solid #000;
}

.dark .hero {
    background-color: #1a1a1a;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Cute background pattern */
.hero::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .hero::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Hero content */
.hero-content {
    position: relative;
    z-index: 1;
}

/* Hero intro */
.hero-intro {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
    padding: 5px 12px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
}

.dark .hero-intro {
    color: #fff;
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

/* Hero title */
.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 1.5rem;
    position: relative;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    font-family: 'Playfair Display', serif;
}

.dark .hero-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Typing container */
.typing-container {
    margin-bottom: 2rem;
    position: relative;
    display: block;
    width: fit-content;
    padding: 8px 15px;
    background-color: #fff;
    border: 2px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    margin-top: 1rem;
    line-height: 1.5;
}

.dark .typing-container {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Typed element */
.typed-element {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    display: inline-block;
    white-space: nowrap;
}

.dark .typed-element {
    color: #fff;
}

/* Fix for typed.js cursor */
.typed-cursor {
    font-size: 1.2rem;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: #333;
}

.dark .typed-cursor {
    color: #fff;
}

/* Hero image */
.hero-image {
    position: relative;
    z-index: 1;
    padding: 20px;
}

.hero-image::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        45deg,
        #FFD1DC,
        #FFD1DC 10px,
        #fff 10px,
        #fff 20px
    );
    z-index: -1;
    border: 3px solid #000;
    border-radius: 0;
    animation: patternMove 20s linear infinite;
}

.dark .hero-image::before {
    background: repeating-linear-gradient(
        45deg,
        rgba(255, 209, 220, 0.3),
        rgba(255, 209, 220, 0.3) 10px,
        rgba(42, 42, 42, 0.5) 10px,
        rgba(42, 42, 42, 0.5) 20px
    );
    border: 3px solid rgba(255, 255, 255, 0.7);
}

.hero-image img {
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
    animation: float 6s ease-in-out infinite;
}

.dark .hero-image img {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

.hero-image img:hover {
    transform: translate(-5px, -5px);
    box-shadow: 15px 15px 0 rgba(0, 0, 0, 0.7);
}

.dark .hero-image img:hover {
    box-shadow: 15px 15px 0 rgba(255, 255, 255, 0.15);
}

/* Decorative elements around hero image */
.hero-image::after {
    content: "♥";
    position: absolute;
    bottom: -10px;
    right: -10px;
    font-size: 3rem;
    color: #FF6B8B;
    z-index: 3;
    animation: pulse 2s ease-in-out infinite;
}

.dark .hero-image::after {
    color: rgba(255, 107, 139, 0.7);
}

.floating-decoration {
    position: absolute;
    z-index: 3;
    animation: float 6s ease-in-out infinite;
}

.floating-decoration.star {
    top: 10%;
    left: 10%;
    font-size: 2rem;
    color: #FFD166;
    animation-delay: 1s;
}

.dark .floating-decoration.star {
    color: rgba(255, 209, 102, 0.7);
}

.floating-decoration.circle {
    top: 20%;
    right: 5%;
    width: 30px;
    height: 30px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 50%;
    animation-delay: 2s;
}

.dark .floating-decoration.circle {
    background-color: rgba(162, 210, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.floating-decoration.square {
    bottom: 15%;
    left: 5%;
    width: 25px;
    height: 25px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    animation-delay: 3s;
}

.dark .floating-decoration.square {
    background-color: rgba(255, 209, 220, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Animation keyframes */
@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-15px);
    }
    100% {
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes patternMove {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 100px 100px;
    }
}

/* Hero buttons */
.hero-buttons {
    margin-top: 2rem;
}

.hero-button {
    display: inline-block;
    padding: 12px 25px;
    background-color: #FFD1DC;
    color: #000;
    font-weight: 700;
    text-decoration: none;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-right: 15px;
    margin-bottom: 15px;
}

.dark .hero-button {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.hero-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .hero-button:hover {
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.hero-button.secondary {
    background-color: #A2D2FF;
}

.dark .hero-button.secondary {
    background-color: rgba(162, 210, 255, 0.3);
}

/* About section */
.about-section {
    position: relative;
    padding: 80px 0;
    background-color: #fff;
    border-bottom: 3px solid #000;
}

.dark .about-section {
    background-color: #2a2a2a;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* About title */
.about-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 2rem;
    position: relative;
    display: inline-block;
    padding: 8px 15px;
    background-color: #A2D2FF;
    border: 3px solid #000;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    font-family: 'Playfair Display', serif;
}

.dark .about-title {
    color: #fff;
    background-color: rgba(162, 210, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

/* About content */
.about-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 2rem;
}

.dark .about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* About image */
.about-image {
    position: relative;
}

.about-image img {
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .about-image img {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

.about-image img:hover {
    transform: translate(-3px, -3px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .about-image img:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Features section */
.features-section {
    position: relative;
    padding: 80px 0;
    background-color: #fffef0;
    border-bottom: 3px solid #000;
}

.dark .features-section {
    background-color: #1a1a1a;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Features title */
.features-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 3rem;
    text-align: center;
    position: relative;
    display: inline-block;
    padding: 8px 15px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    font-family: 'Playfair Display', serif;
}

.dark .features-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

/* Feature card */
.feature-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
}

.dark .feature-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

.feature-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 13px 13px 0 rgba(0, 0, 0, 0.7);
}

.dark .feature-card:hover {
    box-shadow: 13px 13px 0 rgba(255, 255, 255, 0.15);
}

/* Feature icon */
.feature-icon {
    font-size: 2.5rem;
    color: #FFD1DC;
    margin-bottom: 20px;
    display: inline-block;
    padding: 15px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
}

.dark .feature-icon {
    color: rgba(255, 209, 220, 0.7);
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

/* Feature title */
.feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 15px;
}

.dark .feature-title {
    color: #fff;
}

/* Feature description */
.feature-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
}

.dark .feature-description {
    color: rgba(255, 255, 255, 0.7);
}

/* CTA section */
.cta-section {
    position: relative;
    padding: 80px 0;
    background-color: #fff;
    text-align: center;
}

.dark .cta-section {
    background-color: #2a2a2a;
}

/* CTA container */
.cta-container {
    background-color: #FFD1DC;
    border: 3px solid #000;
    border-radius: 0;
    padding: 50px;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    position: relative;
    z-index: 1;
}

.dark .cta-container {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* CTA title */
.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
}

.dark .cta-title {
    color: #fff;
}

/* CTA description */
.cta-description {
    font-size: 1.2rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.dark .cta-description {
    color: rgba(255, 255, 255, 0.9);
}

/* CTA button */
.cta-button {
    display: inline-block;
    padding: 15px 30px;
    background-color: #A2D2FF;
    color: #000;
    font-weight: 700;
    text-decoration: none;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.dark .cta-button {
    background-color: rgba(162, 210, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.cta-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .cta-button:hover {
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Cute decorative elements */
.hero::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .hero::after {
    color: rgba(255, 107, 139, 0.7);
}

.features-section::before {
    content: "♥";
    position: absolute;
    top: 20px;
    left: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .features-section::before {
    color: rgba(255, 107, 139, 0.7);
}

.cta-container::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .cta-container::before {
    color: rgba(255, 107, 139, 0.7);
}

.cta-container::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .cta-container::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Responsive design */
@media (max-width: 991px) {
    .hero-intro {
        font-size: 1.1rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .typing-container {
        padding: 6px 12px;
    }

    .typed-element {
        font-size: 1.1rem;
    }

    .about-title,
    .features-title,
    .cta-title {
        font-size: 2rem;
    }

    .cta-container {
        padding: 30px;
    }

    .floating-decoration.star {
        font-size: 1.8rem;
    }

    .floating-decoration.circle,
    .floating-decoration.square {
        width: 25px;
        height: 25px;
    }
}

@media (max-width: 768px) {
    .hero {
        padding: 60px 0;
    }

    .hero-intro {
        font-size: 1rem;
        padding: 4px 10px;
    }

    .hero-title {
        font-size: 2rem;
        padding: 8px 15px;
    }

    .typing-container {
        padding: 5px 10px;
        margin-bottom: 1.5rem;
    }

    .typed-element {
        font-size: 1rem;
    }

    .hero-image {
        margin-top: 30px;
    }

    .about-section,
    .features-section,
    .cta-section {
        padding: 60px 0;
    }

    .about-title,
    .features-title,
    .cta-title {
        font-size: 1.8rem;
    }

    .feature-card {
        padding: 20px;
    }

    .cta-button {
        padding: 12px 25px;
        font-size: 1.1rem;
    }

    .floating-decoration.star {
        font-size: 1.5rem;
    }

    .floating-decoration.circle,
    .floating-decoration.square {
        width: 20px;
        height: 20px;
    }

    @keyframes float {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-10px);
        }
        100% {
            transform: translateY(0);
        }
    }
}

@media (max-width: 576px) {
    .hero {
        padding: 40px 0;
    }

    .hero-intro {
        font-size: 0.9rem;
        padding: 3px 8px;
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .hero-intro {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }

    .hero-title {
        font-size: 1.8rem;
        padding: 6px 12px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .hero-title {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }

    .typing-container {
        padding: 4px 8px;
        margin-bottom: 1.2rem;
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .typing-container {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }

    .typed-element {
        font-size: 0.9rem;
    }

    .hero-button {
        padding: 10px 20px;
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .about-section,
    .features-section,
    .cta-section {
        padding: 40px 0;
    }

    .about-title,
    .features-title {
        font-size: 1.5rem;
        padding: 6px 12px;
    }

    .cta-title {
        font-size: 1.5rem;
    }

    .cta-description {
        font-size: 1rem;
    }

    .cta-container {
        padding: 25px 15px;
    }

    .cta-button {
        padding: 10px 20px;
        font-size: 1rem;
    }

    .floating-decoration.star {
        font-size: 1.2rem;
    }

    .floating-decoration.circle,
    .floating-decoration.square {
        width: 15px;
        height: 15px;
    }

    .hero-image::after {
        font-size: 2rem;
    }

    @keyframes float {
        0% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-5px);
        }
        100% {
            transform: translateY(0);
        }
    }
}
