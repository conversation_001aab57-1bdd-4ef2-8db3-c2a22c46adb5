<!DOCTYPE html>
<html lang="{{ .Site.LanguageCode }}">

<head>
    <title>{{ .Site.Title }}</title>
    <meta name="description" content="{{ .Site.Params.description }}" />
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width">
    <link rel="icon" href={{ .Site.Params.favicon | default "/fav.png" }} type="image/gif">

    <!-- Fonts - Elegant pairing of serif and sans-serif -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preload"
          as="style"
          href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&family=Outfit:wght@300;400;500;600;700&display=swap"
    >
    <link rel="stylesheet"
          href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&family=Outfit:wght@300;400;500;600;700&display=swap"
          media="print" onload="this.media='all'" />

    <noscript>
      <link
              href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&family=Outfit:wght@300;400;500;600;700&display=swap"
              rel="stylesheet">
    </noscript>

    <!-- font configuration -->
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/font.css" media="all">

    <!-- Internal templates -->
    {{ template "_internal/google_analytics.html" . }}
    {{ template "_internal/opengraph.html" . }}
    {{ template "_internal/twitter_cards.html" . }}

    <!-- stylesheets -->
    {{- if (or (eq .Site.Params.UseBootstrapCDN true) (eq .Site.Params.UseBootstrapCDN "css")) -}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
    {{- else -}}
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/bootstrap-5/css/bootstrap.min.css" media="all">
    {{- end -}}
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/header.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/footer.css" media="all">

    <!-- AOS CSS for scroll animations -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- theme -->
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/theme.css" media="all">

    <!-- Neo-brutalism theme -->
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/neo-brutalism.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/neo-brutalism-grid.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/search-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/search-input-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/table-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/progress-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/education-date-fix.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/experience-fix.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/experience-fix-override.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/experience-fix-final.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/pagination-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/about-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/taxonomy-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/til-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/blog-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/exercises-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/habit-tracker-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/table-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/scroll-navbar.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/preloader.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/landing-neo-cute.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/toc-neo.css" media="all">
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/navbar-fix.css" media="all">

    <!-- Safari and Mobile Compatibility Fixes -->
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/safari-mobile-fixes.css" media="all">

    <!-- Custom Styles -->
    {{ if .Site.Params.customCSS }}
    <link rel="stylesheet" href="{{ .Site.Params.staticPath }}/style.css">
    {{ end }}

    <style>
    :root {
        --text-color: {{ .Site.Params.color.textColor | default "#2d2d2d" }};
        --text-secondary-color: {{ .Site.Params.color.secondaryTextColor | default "#5d5d5d" }};
        --background-color: {{ .Site.Params.color.backgroundColor | default "#fdf8f5" }};
        --secondary-background-color: {{ .Site.Params.color.secondaryBackgroundColor | default "#ffede270" }};
        --primary-color: {{ .Site.Params.color.primaryColor | default "#ff8052" }};
        --secondary-color: {{ .Site.Params.color.secondaryColor | default "#fff6ed" }};
        --text-color-dark: {{ .Site.Params.color.darkmode.textColor | default "#f4f2f0" }};
        --text-secondary-color-dark: {{ .Site.Params.color.darkmode.secondaryTextColor | default "#cdc7c2" }};
        --background-color-dark: {{ .Site.Params.color.darkmode.backgroundColor | default "#1a1715" }};
        --secondary-background-color-dark: {{ .Site.Params.color.darkmode.secondaryBackgroundColor | default "#2a2521" }};
        --primary-color-dark: {{ .Site.Params.color.darkmode.primaryColor | default "#ff9966" }};
        --secondary-color-dark: {{ .Site.Params.color.darkmode.secondaryColor | default "#282420" }};

        /* Neo-brutalism accent colors */
        --accent1: #FFD166;
        --accent2: #06D6A0;
        --accent3: #118AB2;
        --accent4: #EF476F;
        --primary-rgb: 255, 128, 82;

        /* Cute neo-brutalism colors */
        --cute-pink: #FFD1DC;
        --cute-pink-dark: rgba(255, 209, 220, 0.3);
        --cute-heart: #FF6B8B;
        --cute-heart-dark: rgba(255, 107, 139, 0.7);
    }
    </style>

    <style>
    html {
        background-color: var(--background-color) !important;
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
    }

    body {
        font-size: {{ .Site.Params.font.fontSize | default "1rem" }};
        font-weight: {{ .Site.Params.font.fontWeight | default "400" }};
        line-height: {{ .Site.Params.font.lineHeight | default "1.5" }};
        text-align: {{ .Site.Params.font.textAlign | default "left" }};
        overflow-x: hidden;
        width: 100%;
        max-width: 100vw;
        position: relative;
        color: var(--text-color) !important;
    }

    body::-webkit-scrollbar {
        width: .5em;
        height: .5em;
        background-color: var(--background-color);
    }

    ::-webkit-scrollbar-track {
        box-shadow: inset 0 0 6px var(--background-color);
        border-radius: 1rem;
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 1rem;
        background-color: var(--primary-color);
        opacity: 0.5;
        outline: 1px solid var(--background-color);
    }

    #search-content::-webkit-scrollbar {
        width: .5em;
        height: .1em;
        background-color: var(--background-color);
    }

    /* Force text visibility */
    p, h1, h2, h3, h4, h5, h6, span, div, li, a, label {
        color: var(--text-color) !important;
    }

    /* Typography styling for elegant font pairing */
    h1, h2, h3, .navbar-brand, .section-title {
        font-family: 'Playfair Display', serif !important;
        font-weight: 600;
    }

    body, p, a, span, div, li, label, .btn, input,
    .nav-link, .dropdown-item, h4, h5, h6 {
        font-family: 'Outfit', sans-serif !important;
    }

    h1 {
        font-size: 3.5rem;
        font-weight: 700;
        letter-spacing: -0.02em;
    }

    h2 {
        font-size: 2.5rem;
        letter-spacing: -0.01em;
    }

    h3 {
        font-size: 1.8rem;
    }

    .navbar-brand {
        letter-spacing: 0.02em;
    }

    .text-primary, .primary-text {
        color: var(--primary-color) !important;
    }

    .text-secondary {
        color: var(--text-secondary-color) !important;
    }

    /* Update navbar search box with icon */
    .search-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .search-icon {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary-color);
        pointer-events: none;
    }

    @media (max-width: 991px) {
        .search-container {
            width: 100%;
            margin-top: 10px;
        }

        h1 {
            font-size: 2.5rem;
        }

        h2 {
            font-size: 2rem;
        }

        h3 {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 576px) {
        h1 {
            font-size: 2rem;
        }

        h2 {
            font-size: 1.75rem;
        }

        h3 {
            font-size: 1.4rem;
        }
    }

    /* Typing animation styles */
    .typed-element {
        color: var(--primary-color);
        font-weight: 500;
    }

    .typed-cursor {
        color: var(--primary-color);
        opacity: 1;
        animation: blink 0.7s infinite;
    }

    @keyframes blink {
        0% { opacity: 1; }
        50% { opacity: 0; }
        100% { opacity: 1; }
    }

    html.dark .typed-element {
        color: var(--primary-color-dark);
    }

    html.dark .typed-cursor {
        color: var(--primary-color-dark);
    }

    /* Custom navbar styles fix for blurriness */
    </style>

    {{ if .Site.Params.darkMode }}
    <script>
        var isDarkTheme = localStorage.getItem('dark');

        if (isDarkTheme === 'true') {
            document.documentElement.classList.add('dark');
            document.documentElement.classList.remove('light');
        } else {
            document.documentElement.classList.add('light');
            document.documentElement.classList.remove('dark');
        }
    </script>
    {{ end }}

    <!-- Custom cursor script with performance improvements -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Only enable custom cursor on non-touch devices
            if (window.matchMedia("(pointer: fine)").matches) {
                const cursor = document.createElement('div');
                cursor.className = 'cursor-dot';
                document.body.appendChild(cursor);

                const outline = document.createElement('div');
                outline.className = 'cursor-dot-outline';
                document.body.appendChild(outline);

                let cursorVisible = true;
                let cursorEnlarged = false;

                // Cache DOM queries for better performance
                const hoverableElements = 'a, button, .btn, input, textarea, select, .card, .clickable';

                // Mouse movement function with added performance
                document.addEventListener('mousemove', e => {
                    if (cursorVisible) {
                        cursor.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
                        outline.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
                    }
                });

                // Click animation
                document.addEventListener('mousedown', () => {
                    cursor.style.transform += ' scale(0.8)';
                    outline.style.transform += ' scale(0.8)';
                });

                document.addEventListener('mouseup', () => {
                    cursor.style.transform = cursor.style.transform.replace(' scale(0.8)', '');
                    outline.style.transform = outline.style.transform.replace(' scale(0.8)', '');
                });

                // Mouse enter/leave window
                document.addEventListener('mouseenter', () => {
                    cursorVisible = true;
                    cursor.style.opacity = '1';
                    outline.style.opacity = '1';
                });

                document.addEventListener('mouseleave', () => {
                    cursorVisible = false;
                    cursor.style.opacity = '0';
                    outline.style.opacity = '0';
                });

                // Delegated event listeners for hover effects
                document.addEventListener('mouseover', e => {
                    if (e.target.matches(hoverableElements)) {
                        cursor.classList.add('hover');
                        outline.classList.add('hover');
                    }
                });

                document.addEventListener('mouseout', e => {
                    if (e.target.matches(hoverableElements)) {
                        cursor.classList.remove('hover');
                        outline.classList.remove('hover');
                    }
                });
            }
        });
    </script>

    <!-- Add Smooth Scroll -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 60,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>

    <!-- Search functionality -->
    <script src="{{ .Site.Params.staticPath }}/js/search-neo.js"></script>
</head>
