/* Neo-brutalism Exercise Styles with Cute Elements */

/* Exercise section container */
.exercise-section {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .exercise-section {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.exercise-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#A2D2FF 6px, transparent 6px),
        radial-gradient(#A2D2FF 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .exercise-section::before {
    background-image:
        radial-gradient(rgba(162, 210, 255, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(162, 210, 255, 0.3) 6px, transparent 6px);
}

/* Exercise section title */
.exercise-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #A2D2FF;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .exercise-title {
    color: #fff;
    background-color: rgba(162, 210, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Exercise description */
.exercise-description {
    position: relative;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    z-index: 1;
}

.dark .exercise-description {
    color: rgba(255, 255, 255, 0.9);
}

/* Exercise filter container */
.exercise-filter-container {
    position: relative;
    z-index: 1;
    margin-bottom: 30px;
}

/* Exercise filter buttons */
.day-filters .btn {
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    margin: 0 5px 10px 0;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .day-filters .btn {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.day-filters .btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .day-filters .btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.day-filters .btn.active {
    background-color: #A2D2FF;
    color: #000;
}

.dark .day-filters .btn.active {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
}

/* Exercise card */
.exercise-card .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    z-index: 1; /* Ensure proper stacking context */
    cursor: pointer;
}

.dark .exercise-card .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.exercise-card .card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .exercise-card .card:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Fix for clickable elements */
.exercise-card .card-body,
.exercise-card .card-header,
.exercise-card button,
.exercise-card .btn,
.exercise-card input,
.exercise-card .form-range,
.exercise-card .duration-controls {
    position: relative;
    z-index: 2;
    pointer-events: auto;
}

/* Exercise card header */
.exercise-card .card-header {
    background-color: #A2D2FF;
    border-bottom: 3px solid #000;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark .exercise-card .card-header {
    background-color: rgba(162, 210, 255, 0.3);
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Exercise card title */
.exercise-card .card-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #000;
    margin: 0;
    font-family: 'Playfair Display', serif;
}

.dark .exercise-card .card-title {
    color: #fff;
}

/* Exercise duration badge */
.exercise-card .badge {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
}

.dark .exercise-card .badge {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Exercise card image */
.exercise-card .card-img-top {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-bottom: 2px solid #000;
}

.dark .exercise-card .card-img-top {
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

/* Exercise card body */
.exercise-card .card-body {
    padding: 20px;
    color: #333;
}

.dark .exercise-card .card-body {
    color: rgba(255, 255, 255, 0.9);
}

/* Exercise card description */
.exercise-card .card-text {
    margin-bottom: 20px;
    font-size: 0.95rem;
}

/* Exercise day badges */
.exercise-card .badge.bg-secondary {
    background-color: #f0f0f0 !important;
    color: #555 !important;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 3px 8px;
    font-size: 0.75rem;
    margin-right: 5px;
    box-shadow: none;
}

.dark .exercise-card .badge.bg-secondary {
    background-color: #333 !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Exercise timer buttons */
.exercise-card .btn-success,
.exercise-card button.btn-success,
.exercise-card .start-timer-btn {
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    white-space: nowrap;
}

.dark .exercise-card .btn-success,
.dark .exercise-card button.btn-success,
.dark .exercise-card .start-timer-btn {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.exercise-card .btn-success:hover,
.exercise-card button.btn-success:hover,
.exercise-card .start-timer-btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    background-color: #A2D2FF;
    color: #000;
}

.dark .exercise-card .btn-success:hover,
.dark .exercise-card button.btn-success:hover,
.dark .exercise-card .start-timer-btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
}

.exercise-card .btn-danger,
.exercise-card button.btn-danger,
.exercise-card .reset-timer-btn {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    white-space: nowrap;
}

.dark .exercise-card .btn-danger,
.dark .exercise-card button.btn-danger,
.dark .exercise-card .reset-timer-btn {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.exercise-card .btn-danger:hover,
.exercise-card button.btn-danger:hover,
.exercise-card .reset-timer-btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    background-color: #FFD1DC;
    color: #000;
}

.dark .exercise-card .btn-danger:hover,
.dark .exercise-card button.btn-danger:hover,
.dark .exercise-card .reset-timer-btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
}

/* Timer overlay */
.timer-overlay {
    position: absolute;
    top: 0;
    right: 0;
    width: auto;
    height: auto;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    border-bottom-left-radius: 10px;
    border: 2px solid #000;
    border-top: none;
    border-right: none;
}

.dark .timer-overlay {
    background-color: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.7);
    border-top: none;
    border-right: none;
}

.timer-display {
    font-size: 2.5rem;
    font-weight: 700;
    color: #fff;
    padding: 10px 20px;
    border-radius: 0;
}

/* Cute decorative elements */
.exercise-section::after {
    content: "✦";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #A2D2FF;
    z-index: 0;
}

.dark .exercise-section::after {
    color: rgba(162, 210, 255, 0.7);
}

/* Single exercise page */
.exercise-single {
    position: relative;
    padding: 40px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    z-index: 1;
}

.dark .exercise-single {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

.exercise-single::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 2rem;
    color: #A2D2FF;
    z-index: 2;
}

.dark .exercise-single::before {
    color: rgba(162, 210, 255, 0.7);
}

.exercise-single::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 2rem;
    color: #A2D2FF;
    z-index: 2;
}

.dark .exercise-single::after {
    color: rgba(162, 210, 255, 0.7);
}

/* Range slider styling */
.form-range {
    width: 100%;
    height: 1.5rem;
    padding: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
}

.form-range:focus {
    outline: none;
    border: none !important;
    box-shadow: none !important;
}

.form-range::-webkit-slider-thumb {
    width: 1rem;
    height: 1rem;
    margin-top: -0.25rem;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.dark .form-range::-webkit-slider-thumb {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.15);
}

.form-range::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
    -moz-appearance: none;
    appearance: none;
    cursor: pointer;
}

.dark .form-range::-moz-range-thumb {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.15);
}

.form-range::-webkit-slider-runnable-track {
    width: 100%;
    height: 0.5rem;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 0;
}

.dark .form-range::-webkit-slider-runnable-track {
    background-color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.form-range::-moz-range-track {
    width: 100%;
    height: 0.5rem;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 0;
}

.dark .form-range::-moz-range-track {
    background-color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Reset button styling */
.reset-duration-btn {
    background: none !important;
    border: none !important;
    padding: 0 !important;
    color: #A2D2FF !important;
    text-decoration: underline;
    cursor: pointer;
    box-shadow: none !important;
    font-size: 0.8rem;
}

.dark .reset-duration-btn {
    color: rgba(162, 210, 255, 0.7) !important;
}

.reset-duration-btn:hover {
    color: #FF6B8B !important;
    text-decoration: underline;
}

.dark .reset-duration-btn:hover {
    color: rgba(255, 107, 139, 0.7) !important;
}

/* Responsive design */
@media (max-width: 991px) {
    .exercise-section {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .exercise-single {
        padding: 30px;
        margin: 30px 15px;
    }
}

@media (max-width: 768px) {
    .exercise-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .exercise-description {
        font-size: 1rem;
    }

    .exercise-card .card-title {
        font-size: 1.1rem;
    }

    .exercise-card .card-body {
        padding: 15px;
    }

    .timer-display {
        font-size: 2.5rem;
    }

    .exercise-card .d-flex.justify-content-between.align-items-center {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 10px;
    }

    .exercise-card .d-flex.justify-content-between.align-items-center > div:last-child {
        align-self: flex-end;
    }
}

@media (max-width: 576px) {
    .exercise-section {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .exercise-single {
        padding: 20px;
        margin: 20px 10px;
    }

    .exercise-card .card-img-top {
        height: 150px;
    }

    .timer-display {
        font-size: 2rem;
    }

    .exercise-card .btn-success,
    .exercise-card button.btn-success,
    .exercise-card .start-timer-btn,
    .exercise-card .btn-danger,
    .exercise-card button.btn-danger,
    .exercise-card .reset-timer-btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .exercise-card .badge {
        font-size: 0.8rem;
    }
}
