/* Neo-brutalism Gallery Styles with Cute Elements */

/* Gallery section container */
.gallery-section {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .gallery-section {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.gallery-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .gallery-section::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Gallery title */
.gallery-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .gallery-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Gallery description */
.gallery-description {
    position: relative;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    z-index: 1;
}

.dark .gallery-description {
    color: rgba(255, 255, 255, 0.9);
}

/* Gallery filter */
.gallery-filter {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
}

.filter-button {
    padding: 8px 15px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    font-size: 0.9rem;
}

.dark .filter-button {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.filter-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .filter-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.filter-button.active {
    background-color: #FFD1DC;
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .filter-button.active {
    background-color: rgba(255, 209, 220, 0.3);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Gallery grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 25px;
    position: relative;
    z-index: 1;
}

/* Gallery item */
.gallery-item {
    position: relative;
    transition: all 0.5s ease;
    transform: scale(1);
    opacity: 1;
}

.gallery-item.hidden {
    transform: scale(0.8);
    opacity: 0;
    position: absolute;
    pointer-events: none;
}

.gallery-item-inner {
    position: relative;
    overflow: hidden;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    aspect-ratio: 1 / 1;
}

.dark .gallery-item-inner {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.gallery-item:hover .gallery-item-inner {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .gallery-item:hover .gallery-item-inner {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Gallery image */
.gallery-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.05);
}

/* Gallery item overlay */
.gallery-item-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 209, 220, 0.8);
    padding: 15px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
    border-top: 2px solid #000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dark .gallery-item-overlay {
    background-color: rgba(255, 209, 220, 0.3);
    border-top: 2px solid rgba(255, 255, 255, 0.7);
}

.gallery-item:hover .gallery-item-overlay {
    transform: translateY(0);
}

/* Gallery item title */
.gallery-item-title {
    font-size: 1rem;
    font-weight: 600;
    color: #000;
    margin: 0;
}

.dark .gallery-item-title {
    color: #fff;
}

/* Gallery item category */
.gallery-item-category {
    font-size: 0.8rem;
    color: #333;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 2px 8px;
    border-radius: 10px;
    display: inline-block;
    align-self: flex-start;
}

.dark .gallery-item-category {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Gallery decorative elements */
.gallery-decoration {
    position: absolute;
    z-index: 0;
    opacity: 0.5;
    animation: float 6s ease-in-out infinite;
}

.gallery-decoration.star-1 {
    top: 10%;
    right: 5%;
    font-size: 2rem;
    color: #FFD166;
    animation-delay: 0.5s;
}

.gallery-decoration.heart-1 {
    bottom: 15%;
    left: 7%;
    font-size: 2.5rem;
    color: #FF6B8B;
    animation-delay: 1s;
}

.gallery-decoration.circle-1 {
    top: 20%;
    left: 10%;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #A2D2FF;
    animation-delay: 1.5s;
}

.gallery-decoration.square-1 {
    bottom: 25%;
    right: 10%;
    width: 25px;
    height: 25px;
    background-color: #B5EAD7;
    animation-delay: 2s;
}

.gallery-decoration.triangle-1 {
    top: 70%;
    right: 20%;
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-bottom: 30px solid #C7CEEA;
    animation-delay: 2.5s;
}

@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-15px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

/* Lightbox content */
.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 80vh;
    border: 5px solid #000;
    border-radius: 0;
    box-shadow: 15px 15px 0 rgba(255, 209, 220, 0.3);
    overflow: hidden;
}

.dark .lightbox-content {
    border: 5px solid rgba(255, 255, 255, 0.7);
    box-shadow: 15px 15px 0 rgba(255, 209, 220, 0.15);
}

/* Lightbox image */
.lightbox-image {
    display: block;
    max-width: 100%;
    max-height: 80vh;
}

/* Lightbox caption */
.lightbox-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 209, 220, 0.8);
    color: #000;
    padding: 15px;
    text-align: center;
    font-weight: 600;
    border-top: 3px solid #000;
}

.dark .lightbox-caption {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border-top: 3px solid rgba(255, 255, 255, 0.7);
}

/* Lightbox category */
.lightbox-category {
    display: inline-block;
    font-size: 0.8rem;
    font-weight: normal;
    background-color: rgba(255, 255, 255, 0.5);
    color: #333;
    padding: 2px 8px;
    border-radius: 10px;
    margin-left: 8px;
}

.dark .lightbox-category {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
}

/* Lightbox close button */
.lightbox-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    z-index: 10;
}

.dark .lightbox-close {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.lightbox-close:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .lightbox-close:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.lightbox-close::before,
.lightbox-close::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 3px;
    background-color: #000;
}

.dark .lightbox-close::before,
.dark .lightbox-close::after {
    background-color: #fff;
}

.lightbox-close::before {
    transform: rotate(45deg);
}

.lightbox-close::after {
    transform: rotate(-45deg);
}

/* Lightbox navigation */
.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    z-index: 10;
}

.dark .lightbox-nav {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.lightbox-nav:hover {
    transform: translateY(-50%) translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .lightbox-nav:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.lightbox-prev {
    left: 20px;
}

.lightbox-next {
    right: 20px;
}

.lightbox-nav::before {
    content: "";
    position: absolute;
    width: 15px;
    height: 15px;
    border-style: solid;
    border-color: #000;
    border-width: 0 3px 3px 0;
}

.dark .lightbox-nav::before {
    border-color: #fff;
}

.lightbox-prev::before {
    transform: rotate(135deg);
    margin-left: 5px;
}

.lightbox-next::before {
    transform: rotate(-45deg);
    margin-right: 5px;
}

/* Cute decorative elements */
.gallery-section::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .gallery-section::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Responsive design */
@media (max-width: 991px) {
    .gallery-section {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .gallery-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .gallery-description {
        font-size: 1rem;
    }

    .lightbox-content {
        max-width: 95%;
    }

    .lightbox-nav {
        width: 40px;
        height: 40px;
    }
}

@media (max-width: 576px) {
    .gallery-section {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }

    .gallery-item {
        border-width: 2px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .gallery-item {
        border-width: 2px;
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }

    .lightbox-close,
    .lightbox-nav {
        width: 35px;
        height: 35px;
    }
}
