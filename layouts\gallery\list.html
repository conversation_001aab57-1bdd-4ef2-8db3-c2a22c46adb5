{{ define "head" }}
<meta name="description" content="{{ .Title }} - {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/gallery-neo-cute.css" media="all">
<script src="{{ .Site.Params.staticPath }}/js/safari-mobile-performance.js" defer></script>
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="gallery-section">
    <div class="container">
        <div class="text-center">
            <h1 class="gallery-title" data-aos="fade-up" data-aos-duration="800">{{ .Title }}</h1>
            {{ if .Description }}
            <p class="gallery-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">{{ .Description }}</p>
            {{ end }}
        </div>

        <div class="gallery-filter" data-aos="fade-up" data-aos-duration="800" data-aos-delay="150">
            <button class="filter-button active" data-filter="all">All</button>
            {{ $categories := slice }}
            {{ range .Pages }}
                {{ if .Params.category }}
                    {{ $categories = $categories | append .Params.category }}
                {{ end }}
            {{ end }}
            {{ range ($categories | uniq) }}
                <button class="filter-button" data-filter="{{ . | urlize }}">{{ . }}</button>
            {{ end }}
        </div>

        <div class="gallery-grid">
            {{ range .Pages }}
            <div class="gallery-item {{ with .Params.category }}filter-{{ . | urlize }}{{ end }}" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="gallery-item-inner">
                    <img src="{{ .Params.image | default "/images/default.jpg" }}" alt="{{ .Title }}" class="gallery-image">
                    <div class="gallery-item-overlay">
                        <h4 class="gallery-item-title">{{ .Title }}</h4>
                        {{ if .Params.category }}
                        <span class="gallery-item-category">{{ .Params.category }}</span>
                        {{ end }}
                    </div>
                </div>
            </div>
            {{ end }}
        </div>
    </div>

    <!-- Decorative elements -->
    <div class="gallery-decoration star-1">★</div>
    <div class="gallery-decoration heart-1">♥</div>
    <div class="gallery-decoration circle-1"></div>
    <div class="gallery-decoration square-1"></div>
    <div class="gallery-decoration triangle-1"></div>
</section>

<script src="{{ .Site.Params.staticPath }}/js/gallery-lightbox.js"></script>
<script src="{{ .Site.Params.staticPath }}/js/gallery-filter.js"></script>
{{ end }}