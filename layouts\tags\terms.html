{{ define "head" }}
<meta name="description" content="Tags for {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/taxonomy-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
Tags | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="taxonomy-section">
    <div class="container">
        <div class="text-center">
            <h1 class="taxonomy-title" data-aos="fade-up" data-aos-duration="800">Tags</h1>
            <p class="taxonomy-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                Browse all the tags used across the blog posts
            </p>
        </div>

        <div class="tag-cloud" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            {{ range .Data.Terms.ByCount }}
            {{ $fontSize := add 0.8 (mul 0.1 (math.Log .Count)) }}
            <a href="{{ .Page.Permalink }}" class="tag-cloud-tag" style="--tag-size: {{ $fontSize }}rem;">
                <i class="fas fa-tag taxonomy-icon"></i>
                {{ .Page.Title }}
                <span class="taxonomy-count">{{ .Count }}</span>
            </a>
            {{ end }}
        </div>
    </div>
</section>
{{ end }}
