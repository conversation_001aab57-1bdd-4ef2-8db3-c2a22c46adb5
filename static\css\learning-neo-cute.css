/* Neo-brutalism Learning Section */
#learning {
    background-color: var(--background-color);
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    padding: 30px 20px;
    position: relative;
    overflow: hidden;
}

.dark #learning {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Decorative elements */
#learning::before {
    content: "📚";
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(15deg);
}

#learning::after {
    content: "🎓";
    position: absolute;
    bottom: 20px;
    left: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(-15deg);
}

/* Section title */
#learning h3 {
    margin-bottom: 2.5rem;
    position: relative;
    font-weight: 700;
    color: var(--text-color);
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 3px solid #000;
    z-index: 1;
}

.dark #learning h3 {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Learning index */
#learning .index {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: #B5EAD7;
    border: 3px solid #000;
    border-radius: 0;
    font-weight: 700;
    font-size: 1.5rem;
    color: #000;
}

.dark #learning .index {
    background-color: rgba(181, 234, 215, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

/* Learning cards */
#learning .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.dark #learning .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

#learning .card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
}

.dark #learning .card:hover {
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Card content */
#learning .card-body {
    padding: 1.5rem;
}

#learning .card-title {
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    position: relative;
}

#learning .card-title::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #A2D2FF;
}

.dark #learning .card-title::after {
    background-color: rgba(162, 210, 255, 0.7);
}

#learning h6 {
    color: var(--text-secondary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#learning .float-end small {
    background-color: #FFD1DC;
    color: #000;
    padding: 2px 8px;
    border-radius: 0;
    border: 2px solid #000;
    font-weight: 600;
}

.dark #learning .float-end small {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

#learning .learning-content {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
}

#learning .learning-content ul {
    padding-left: 1.5rem;
}

#learning .learning-content li {
    margin-bottom: 0.5rem;
}

/* Featured link button */
#learning .btn {
    background-color: #A2D2FF;
    color: #000;
    font-weight: 600;
    padding: 8px 15px;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark #learning .btn {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

#learning .btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark #learning .btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #learning {
        margin: 30px 15px;
        padding: 25px 15px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }
    
    #learning .card {
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning .card {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #learning .card:hover {
        transform: translate(-3px, -3px);
        box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning .card:hover {
        box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    }
    
    #learning .card-body {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    #learning {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #learning h3 {
        font-size: 1.5rem;
    }
    
    #learning .index {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }
    
    #learning .card {
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning .card {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
    
    #learning .card:hover {
        transform: translate(-2px, -2px);
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #learning .card:hover {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
}
