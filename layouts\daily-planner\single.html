{{ define "head" }}
<meta name="description" content="Daily Planner - Organize your day with a drag-and-drop schedule">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/daily-planner-neo-cute.css" media="all">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<!-- fontawesome -->
<script defer src="{{ .Site.Params.staticPath }}/fontawesome-5/all-5.15.4.js"></script>
{{ end }}

{{ define "title" }}
Daily Planner | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="planner-container">
    <div class="container">
        <div class="text-center">
            <h1 class="planner-title" data-aos="fade-up" data-aos-duration="800">Daily Planner</h1>
            <p class="planner-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                Organize your day with this simple drag-and-drop planner. Add tasks and drag them to schedule your day. Your plan will be saved temporarily and will disappear after 24 hours.
            </p>
        </div>

        <div class="planner-layout">
            <!-- Task Panel -->
            <div class="task-panel" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="task-panel-header">
                    <h2 class="task-panel-title">Tasks</h2>
                </div>
                <form id="task-form" class="task-form">
                    <input type="text" id="task-input" class="task-input" placeholder="Add a new task">
                    <button type="submit" class="task-add">Add</button>
                </form>
                <div id="task-items" class="task-items"></div>
            </div>

            <!-- Schedule Panel -->
            <div class="schedule-panel" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                <div class="schedule-panel-header">
                    <h2 class="schedule-panel-title">Today's Schedule</h2>
                    <div id="schedule-date" class="schedule-date"></div>
                </div>
                <div class="schedule-grid">
                    <div class="schedule-hours">
                        <div class="schedule-hour">8:00 AM</div>
                        <div class="schedule-hour">9:00 AM</div>
                        <div class="schedule-hour">10:00 AM</div>
                        <div class="schedule-hour">11:00 AM</div>
                        <div class="schedule-hour">12:00 PM</div>
                        <div class="schedule-hour">1:00 PM</div>
                        <div class="schedule-hour">2:00 PM</div>
                        <div class="schedule-hour">3:00 PM</div>
                        <div class="schedule-hour">4:00 PM</div>
                        <div class="schedule-hour">5:00 PM</div>
                        <div class="schedule-hour">6:00 PM</div>
                        <div class="schedule-hour">7:00 PM</div>
                    </div>
                    <div class="schedule-slots">
                        <div class="schedule-slot" data-slot="0"></div>
                        <div class="schedule-slot" data-slot="1"></div>
                        <div class="schedule-slot" data-slot="2"></div>
                        <div class="schedule-slot" data-slot="3"></div>
                        <div class="schedule-slot" data-slot="4"></div>
                        <div class="schedule-slot" data-slot="5"></div>
                        <div class="schedule-slot" data-slot="6"></div>
                        <div class="schedule-slot" data-slot="7"></div>
                        <div class="schedule-slot" data-slot="8"></div>
                        <div class="schedule-slot" data-slot="9"></div>
                        <div class="schedule-slot" data-slot="10"></div>
                        <div class="schedule-slot" data-slot="11"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="clear-notice" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
            <i class="fas fa-info-circle"></i> Your planner is saved in your browser and will be automatically cleared after 24 hours.
        </div>

        <div class="affirmations" data-aos="fade-up" data-aos-duration="800" data-aos-delay="500">
            <div id="affirmation-text" class="affirmation-text"></div>
            <button id="affirmation-button" class="affirmation-button">New Affirmation</button>
        </div>
    </div>
</section>

<script src="{{ .Site.Params.staticPath }}/js/daily-planner.js"></script>
{{ end }}
