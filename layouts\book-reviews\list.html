{{ define "head" }}
<meta name="description" content="{{ .Title }} of {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/book-reviews-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="book-reviews-container">
    <div class="container">
        <div class="text-center">
            <h1 class="book-reviews-title" data-aos="fade-up" data-aos-duration="800">{{ .Title }}</h1>
            <p class="book-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                Explore my collection of book reviews and recommendations. These are books I've read and found valuable.
            </p>
        </div>

        <div class="row">
            {{ range .Paginator.Pages }}
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="book-card">
                    {{ if .Params.image }}
                    <div class="book-card-image">
                        <a href="{{ .RelPermalink }}">
                            <img src="{{ .Params.image }}" alt="{{ .Title }}" loading="lazy">
                        </a>
                    </div>
                    {{ end }}
                    <div class="book-card-content">
                        <a href="{{ .RelPermalink }}">
                            <h2 class="book-card-title">{{ .Title }}</h2>
                        </a>
                        {{ if .Params.author }}
                        <div class="book-card-author">by {{ .Params.author }}</div>
                        {{ end }}
                        <div class="book-card-description">
                            {{ if .Params.description }}
                            {{ .Params.description | truncate 150 }}
                            {{ else if .IsPage }}
                            {{ .Summary | truncate 150 }}
                            {{ end }}
                        </div>
                        {{ if .Params.rating }}
                        <div class="book-card-rating">
                            {{ $rating := int .Params.rating }}
                            {{ range $i, $e := (seq 5) }}
                                {{ if le $i $rating }}
                                <span class="star">★</span>
                                {{ else }}
                                <span class="star" style="opacity: 0.3;">★</span>
                                {{ end }}
                            {{ end }}
                        </div>
                        {{ end }}
                        {{ if .Params.tags }}
                        <div class="book-card-tags">
                            {{ range first 3 .Params.tags }}
                            <span class="book-card-tag">{{ . }}</span>
                            {{ end }}
                        </div>
                        {{ end }}
                        <div class="book-card-footer">
                            <a href="{{ .RelPermalink }}" class="book-card-button">Read Review</a>
                        </div>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        {{ if gt .Paginator.TotalPages 1 }}
        <div class="row">
            <div class="col-12">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center book-pagination">
                        {{ if .Paginator.HasPrev }}
                        <li class="page-item">
                            <a class="page-link" href="{{ .Paginator.Prev.URL }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {{ else }}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {{ end }}

                        {{ range .Paginator.Pagers }}
                        <li class="page-item {{ if eq . $.Paginator }}active{{ end }}">
                            <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                        </li>
                        {{ end }}

                        {{ if .Paginator.HasNext }}
                        <li class="page-item">
                            <a class="page-link" href="{{ .Paginator.Next.URL }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {{ else }}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        {{ end }}
                    </ul>
                </nav>
            </div>
        </div>
        {{ end }}
    </div>
</section>
{{ end }}
