/* Neo-brutalism About Me Section with Cute Elements */

/* About section container */
#about {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark #about {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
#about::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark #about::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Section title */
#about .section-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark #about .section-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* About image wrapper */
.about-image-wrapper {
    position: relative;
    padding: 15px;
    z-index: 1;
}

/* About image card */
.about-image-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 15px;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    position: relative;
    transition: all 0.3s ease;
    z-index: 1;
}

.dark .about-image-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

.about-image-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 13px 13px 0 rgba(0, 0, 0, 0.7);
}

.dark .about-image-card:hover {
    box-shadow: 13px 13px 0 rgba(255, 255, 255, 0.15);
}

/* About image container */
.about-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 0;
}

/* About image */
.about-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.5s ease;
    transform: scale(1);
    border: 2px solid #000;
}

.dark .about-image {
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.about-image-container:hover .about-image {
    transform: scale(1.05);
}

/* Image effect */
.image-effect {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 100%;
    height: 100%;
    background-color: #FFD1DC;
    opacity: 0.2;
    z-index: -1;
    transition: all 0.3s ease;
}

.dark .image-effect {
    background-color: rgba(255, 209, 220, 0.3);
}

.about-image-container:hover .image-effect {
    top: 8px;
    left: 8px;
}

/* Cute decorative elements */
.about-image-wrapper::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .about-image-wrapper::before {
    color: rgba(255, 107, 139, 0.7);
}

.about-image-wrapper::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .about-image-wrapper::after {
    color: rgba(255, 107, 139, 0.7);
}

/* About content */
.about-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 30px;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    height: 100%;
    position: relative;
    transition: all 0.3s ease;
    z-index: 1;
}

.dark .about-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

.about-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 13px 13px 0 rgba(0, 0, 0, 0.7);
}

.dark .about-card:hover {
    box-shadow: 13px 13px 0 rgba(255, 255, 255, 0.15);
}

/* About content text */
.about-content {
    font-size: 1.05rem;
    line-height: 1.8;
    color: #333;
    position: relative;
    z-index: 1;
}

.dark .about-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Cute corner decorations for content */
.about-content::before {
    content: "♥";
    position: absolute;
    top: -20px;
    left: -20px;
    font-size: 1.5rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .about-content::before {
    color: rgba(255, 107, 139, 0.7);
}

.about-content::after {
    content: "♥";
    position: absolute;
    bottom: -20px;
    right: -20px;
    font-size: 1.5rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .about-content::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Skills section */
.skills-title {
    margin-top: 2rem;
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: #333;
    position: relative;
    display: inline-block;
    padding: 8px 15px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
}

.dark .skills-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

/* Skills container */
.skills-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 1.5rem;
}

/* Skill card */
.skill-card {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .skill-card {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

.skill-card:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
}

.dark .skill-card:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
}

/* Skill icon */
.skill-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 50%;
    font-size: 1.2rem;
    color: #333;
}

.dark .skill-icon {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

/* Skill info */
.skill-info {
    flex: 1;
}

/* Skill name */
.skill-name {
    margin: 0 0 5px 0;
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.dark .skill-name {
    color: rgba(255, 255, 255, 0.9);
}

/* Skill progress container */
.skill-progress-container {
    width: 100%;
    height: 8px;
    background-color: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #000;
}

.dark .skill-progress-container {
    background-color: #3a3a3a;
    border: 1px solid rgba(255, 255, 255, 0.7);
}

/* Skill progress bar */
.skill-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #FF6B8B, #FFD1DC);
    border-radius: 4px;
    position: relative;
    transform-origin: left;
    animation: skillProgress 1.5s ease-in-out forwards;
}

.dark .skill-progress-bar {
    background: linear-gradient(90deg, rgba(255, 107, 139, 0.7), rgba(255, 209, 220, 0.3));
}

@keyframes skillProgress {
    0% {
        transform: scaleX(0);
        opacity: 0.5;
    }
    100% {
        transform: scaleX(1);
        opacity: 1;
    }
}

/* Responsive design */
@media (max-width: 991px) {
    .about-image-wrapper {
        max-width: 80%;
        margin: 0 auto 30px;
    }

    .about-card, .about-image-card {
        padding: 20px;
    }

    .skills-container {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    #about {
        padding: 40px 0;
        margin: 30px 15px;
    }

    .about-content {
        font-size: 1rem;
    }

    .skill-card {
        padding: 12px;
    }

    .skill-icon {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }

    .about-image-wrapper::before,
    .about-image-wrapper::after {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .about-card, .about-image-card {
        padding: 15px;
    }

    .skills-container {
        grid-template-columns: 1fr;
    }

    #about .section-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }
}

@media (min-width: 1500px) {
    .about-content {
        font-size: 1.15rem;
    }

    .about-card, .about-image-card {
        padding: 40px;
    }

    .skill-icon {
        width: 50px;
        height: 50px;
        font-size: 1.4rem;
    }
}
