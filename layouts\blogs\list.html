{{ define "head" }}
<meta name="description" content="{{ .Title }} of {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/blog-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="blog-list-container">
    <div class="container">
        <div class="text-center">
            <h1 class="blog-list-title" data-aos="fade-up" data-aos-duration="800">{{ .Title }}</h1>
            <p class="blog-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                Explore our collection of articles and insights
            </p>
        </div>

        <div class="row">
            {{ range .Paginator.Pages }}
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="blog-card">
                    {{ if and (not (.Site.Params.listPages.disableFeaturedImage | default false)) (.Params.image) }}
                    <div class="blog-card-image">
                        <a href="{{ .RelPermalink }}">
                            <img src="{{ .Params.image }}" alt="{{ .Title }}" loading="lazy">
                        </a>
                    </div>
                    {{ end }}
                    <div class="blog-card-content">
                        <a href="{{ .RelPermalink }}">
                            <h2 class="blog-card-title">{{ .Title }}</h2>
                        </a>
                        <div class="blog-card-description">
                            {{ if .Params.description }}
                            {{ .Params.description | truncate 150 }}
                            {{ else if .IsPage }}
                            {{ .Summary | truncate 150 }}
                            {{ end }}
                        </div>
                        <div class="blog-card-footer">
                            <div class="blog-card-date">
                                <i class="far fa-calendar-alt"></i>
                                {{ .Date.Format (.Site.Params.datesFormat.articleList | default "January 2, 2006") }}
                            </div>
                            <a href="{{ .RelPermalink }}" class="blog-card-read">{{ .Site.Params.terms.read | default "Read" }}</a>
                        </div>
                        {{ if .Params.tags }}
                        <div class="blog-card-tags">
                            {{ range .Params.tags }}
                            <span class="blog-card-tag">{{ . }}</span>
                            {{ end }}
                        </div>
                        {{ end }}
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <!-- Pagination -->
        {{ if gt .Paginator.TotalPages 1 }}
        <div class="blog-pagination" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
            <ul class="pagination">
                {{ if .Paginator.HasPrev }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Prev.URL }}" aria-label="Previous">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                </li>
                {{ end }}

                {{ $paginator := .Paginator }}
                {{ $adjacent_links := 2 }}
                {{ $max_links := (add (mul $adjacent_links 2) 1) }}
                {{ $lower_limit := (add $adjacent_links 1) }}
                {{ $upper_limit := (sub $paginator.TotalPages $adjacent_links) }}

                {{ if gt $paginator.TotalPages $max_links }}
                    {{ if le $paginator.PageNumber $lower_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if le .PageNumber $max_links }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else if ge $paginator.PageNumber $upper_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if gt .PageNumber (sub $paginator.TotalPages $max_links) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else }}
                        {{ range $paginator.Pagers }}
                            {{ if and (ge .PageNumber (sub $paginator.PageNumber $adjacent_links)) (le .PageNumber (add $paginator.PageNumber $adjacent_links)) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ end }}
                {{ else }}
                    {{ range $paginator.Pagers }}
                        <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                            <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                        </li>
                    {{ end }}
                {{ end }}

                {{ if .Paginator.HasNext }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Next.URL }}" aria-label="Next">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                </li>
                {{ end }}
            </ul>
        </div>
        {{ end }}
    </div>
</section>
{{ end }}
