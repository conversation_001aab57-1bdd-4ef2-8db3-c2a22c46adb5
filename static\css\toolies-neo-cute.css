/* Neo-brutalism Toolies Styles */

/* Toolies container */
.toolies-container {
    position: relative;
    padding: 40px 20px;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .toolies-container {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.toolies-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .toolies-container::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Toolies title */
.toolies-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .toolies-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Toolies description */
.toolies-description {
    position: relative;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
}

.dark .toolies-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Tool cards container */
.tool-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    position: relative;
    z-index: 1;
}

/* Tool card */
.tool-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    padding: 20px;
    width: 100%;
    max-width: 350px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dark .tool-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.tool-card:hover {
    transform: translate(-3px, -3px);
    box-shadow: 9px 9px 0 rgba(0, 0, 0, 0.7);
}

.dark .tool-card:hover {
    box-shadow: 9px 9px 0 rgba(255, 255, 255, 0.15);
}

/* Tool card header */
.tool-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
}

.dark .tool-card-header {
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.tool-card-icon {
    font-size: 1.8rem;
    margin-right: 15px;
    color: #FFD166;
}

.dark .tool-card-icon {
    color: rgba(255, 209, 102, 0.7);
}

.tool-card-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
    color: #000;
}

.dark .tool-card-title {
    color: #fff;
}

/* Tool card content */
.tool-card-content {
    margin-bottom: 15px;
}

/* Positive Affirmations Generator */
.affirmation-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.affirmation-intro {
    font-size: 1rem;
    margin-bottom: 5px;
}

.affirmation-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.affirmation-category-btn {
    padding: 8px 12px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-category-btn {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-category-btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-category-btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-category-btn.active {
    background-color: #B5EAD7;
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-category-btn.active {
    background-color: rgba(181, 234, 215, 0.3);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-display {
    padding: 15px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    font-style: italic;
    color: #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    margin: 10px 0;
}

.dark .affirmation-display {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 10px;
}

.affirmation-button {
    padding: 8px 15px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-button {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-saved {
    margin-top: 20px;
    border-top: 2px dashed #000;
    padding-top: 15px;
}

.dark .affirmation-saved {
    border-top: 2px dashed rgba(255, 255, 255, 0.7);
}

.affirmation-saved h3 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: #000;
}

.dark .affirmation-saved h3 {
    color: #fff;
}

.saved-affirmations-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 150px;
    overflow-y: auto;
}

.saved-affirmation-item {
    background-color: #f0f0f0;
    border: 2px solid #000;
    border-radius: 0;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    font-style: italic;
}

.dark .saved-affirmation-item {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.saved-affirmation-text {
    flex: 1;
}

.saved-affirmation-remove {
    background-color: transparent;
    border: none;
    color: #000;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0 5px;
}

.dark .saved-affirmation-remove {
    color: #fff;
}

.no-saved-affirmations {
    font-style: italic;
    color: #666;
    text-align: center;
}

.dark .no-saved-affirmations {
    color: rgba(255, 255, 255, 0.6);
}

/* Decision helper */
.decision-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.decision-input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #000;
    border-radius: 0;
    font-size: 1rem;
    background-color: #fff;
}

.dark .decision-input {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

.decision-options {
    display: flex;
    flex-direction: column;
    gap: 5px;
    margin: 10px 0;
    max-height: 150px;
    overflow-y: auto;
}

.decision-option {
    display: flex;
    align-items: center;
    gap: 10px;
}

.decision-option-text {
    flex: 1;
    padding: 5px 10px;
    border: 2px solid #000;
    border-radius: 0;
    background-color: #fff;
}

.dark .decision-option-text {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

.decision-option-remove {
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
}

.dark .decision-option-remove {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.decision-buttons {
    display: flex;
    gap: 10px;
}

.decision-add {
    flex: 1;
    padding: 8px 15px;
    background-color: #B5EAD7;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .decision-add {
    background-color: rgba(181, 234, 215, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.decision-decide {
    flex: 1;
    padding: 8px 15px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .decision-decide {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.decision-add:hover, .decision-decide:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .decision-add:hover, .dark .decision-decide:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.decision-result {
    margin-top: 15px;
    padding: 15px;
    background-color: #FFD166;
    border: 2px solid #000;
    text-align: center;
    font-weight: 700;
    font-size: 1.1rem;
    display: none;
}

.dark .decision-result {
    background-color: rgba(255, 209, 102, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Breathing exercise */
.breathing-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.breathing-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #A2D2FF;
    border: 3px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 700;
    color: #000;
    transition: transform 4s ease-in-out, background-color 4s ease-in-out;
}

.dark .breathing-circle {
    background-color: rgba(162, 210, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.breathing-circle.inhale {
    transform: scale(1.5);
    background-color: #B5EAD7;
}

.dark .breathing-circle.inhale {
    background-color: rgba(181, 234, 215, 0.3);
}

.breathing-circle.exhale {
    transform: scale(1);
    background-color: #A2D2FF;
}

.dark .breathing-circle.exhale {
    background-color: rgba(162, 210, 255, 0.3);
}

.breathing-text {
    font-size: 1.2rem;
    font-weight: 600;
    text-align: center;
}

.breathing-controls {
    display: flex;
    gap: 10px;
}

.breathing-button {
    padding: 8px 15px;
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .breathing-button {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.breathing-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .breathing-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Meditation Timer */
.meditation-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.meditation-timer {
    font-size: 3rem;
    font-weight: 700;
    color: #000;
    background-color: #FFD1DC;
    border: 3px solid #000;
    border-radius: 0;
    padding: 15px 30px;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    font-family: monospace;
}

.dark .meditation-timer {
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.meditation-quote {
    font-size: 1.1rem;
    font-style: italic;
    text-align: center;
    color: #555;
    margin: 10px 0;
    padding: 10px;
    border-left: 3px solid #000;
    background-color: rgba(255, 255, 255, 0.5);
}

.dark .meditation-quote {
    color: rgba(255, 255, 255, 0.7);
    border-left: 3px solid rgba(255, 255, 255, 0.7);
    background-color: rgba(255, 255, 255, 0.05);
}

.meditation-controls {
    display: flex;
    gap: 10px;
}

.meditation-button {
    padding: 8px 15px;
    background-color: #C7CEEA;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .meditation-button {
    background-color: rgba(199, 206, 234, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.meditation-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .meditation-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.meditation-duration {
    margin-top: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.meditation-select {
    padding: 5px 10px;
    border: 2px solid #000;
    border-radius: 0;
    background-color: #fff;
}

.dark .meditation-select {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

/* Gratitude Journal */
.gratitude-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.gratitude-intro {
    font-size: 1rem;
    margin-bottom: 10px;
}

.gratitude-entries {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.gratitude-entry {
    display: flex;
    align-items: center;
    gap: 10px;
}

.gratitude-entry label {
    font-weight: 700;
    color: #000;
}

.dark .gratitude-entry label {
    color: #fff;
}

.gratitude-input {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #000;
    border-radius: 0;
    font-size: 1rem;
    background-color: #fff;
}

.dark .gratitude-input {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

.gratitude-button {
    align-self: flex-start;
    padding: 8px 15px;
    background-color: #B5EAD7;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    margin-top: 10px;
}

.dark .gratitude-button {
    background-color: rgba(181, 234, 215, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.gratitude-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .gratitude-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.gratitude-message {
    padding: 10px;
    background-color: #B5EAD7;
    border: 2px solid #000;
    text-align: center;
    font-weight: 600;
    display: none;
}

.dark .gratitude-message {
    background-color: rgba(181, 234, 215, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Mood Tracker */
.mood-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mood-question {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.mood-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.mood-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 10px;
    border: 2px solid #000;
    border-radius: 0;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dark .mood-option {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.mood-option:hover {
    transform: translate(-2px, -2px);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .mood-option:hover {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.mood-option.selected {
    background-color: #FFD166;
    transform: translate(-2px, -2px);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .mood-option.selected {
    background-color: rgba(255, 209, 102, 0.3);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.mood-emoji {
    font-size: 1.8rem;
}

.mood-label {
    font-size: 0.8rem;
    font-weight: 600;
}

.mood-note {
    margin-top: 15px;
}

.mood-note label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.mood-note-input {
    width: 100%;
    height: 80px;
    padding: 8px 12px;
    border: 2px solid #000;
    border-radius: 0;
    font-size: 1rem;
    background-color: #fff;
    resize: none;
}

.dark .mood-note-input {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

.mood-button {
    align-self: flex-start;
    padding: 8px 15px;
    background-color: #FFD166;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    margin-top: 10px;
}

.dark .mood-button {
    background-color: rgba(255, 209, 102, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.mood-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .mood-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.mood-message {
    padding: 10px;
    background-color: #FFD166;
    border: 2px solid #000;
    text-align: center;
    font-weight: 600;
    display: none;
}

.dark .mood-message {
    background-color: rgba(255, 209, 102, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Responsive design */
@media (max-width: 991px) {
    .toolies-container {
        margin: 30px 15px;
        padding: 30px 15px;
        box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .toolies-container {
        box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    }

    .toolies-title {
        font-size: 1.8rem;
    }

    .tool-card {
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .tool-card {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }

    .tool-card:hover {
        box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .tool-card:hover {
        box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 768px) {
    .toolies-container {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .toolies-container {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }

    .toolies-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .toolies-description {
        font-size: 1rem;
    }

    .tool-card {
        padding: 15px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .tool-card {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }

    .tool-card:hover {
        box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .tool-card:hover {
        box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    }

    .tool-card-icon {
        font-size: 1.5rem;
    }

    .tool-card-title {
        font-size: 1.1rem;
    }

    .breathing-circle {
        width: 120px;
        height: 120px;
    }
}

@media (max-width: 576px) {
    .toolies-container {
        margin: 15px 5px;
        padding: 15px 5px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .toolies-container {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }

    .toolies-title {
        font-size: 1.3rem;
        padding: 6px 12px;
    }

    .converter-row {
        flex-direction: column;
    }

    .decision-buttons {
        flex-direction: column;
    }
}
