{{ define "head" }}
<meta name="description" content="Boardy - A simple whiteboard to jot your ideas">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/boardy-neo-cute.css" media="all">
<!-- fontawesome -->
<script defer src="{{ .Site.Params.staticPath }}/fontawesome-5/all-5.15.4.js"></script>
{{ end }}

{{ define "title" }}
Boardy | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="boardy-container">
    <div class="container">
        <div class="text-center">
            <h1 class="boardy-title" data-aos="fade-up" data-aos-duration="800">Boardy</h1>
            <p class="boardy-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                A simple whiteboard to jot your ideas on the go. Draw, write, or add emojis to express your thoughts. Your drawings will be saved temporarily and will disappear after 24 hours.
            </p>
        </div>

        <div class="tools-container" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <button id="pen-button" class="tool-button">
                <i class="fas fa-pen"></i> Pen
            </button>
            <button id="eraser-button" class="tool-button">
                <i class="fas fa-eraser"></i> Eraser
            </button>
            <button id="text-button" class="tool-button">
                <i class="fas fa-font"></i> Text
            </button>
            <button id="emoji-button" class="tool-button">
                <i class="far fa-smile"></i> Emoji
            </button>
            <button id="shape-button" class="tool-button">
                <i class="fas fa-shapes"></i> Shapes
            </button>
            <button id="undo-button" class="tool-button">
                <i class="fas fa-undo"></i> Undo
            </button>
            <button id="clear-button" class="tool-button">
                <i class="fas fa-trash"></i> Clear
            </button>
            <button id="save-button" class="tool-button">
                <i class="fas fa-download"></i> Save
            </button>
        </div>

        <div id="emoji-picker" class="emoji-picker">
            <span class="emoji-option">😊</span>
            <span class="emoji-option">😂</span>
            <span class="emoji-option">❤️</span>
            <span class="emoji-option">👍</span>
            <span class="emoji-option">🎉</span>
            <span class="emoji-option">🌟</span>
            <span class="emoji-option">🔥</span>
            <span class="emoji-option">💡</span>
            <span class="emoji-option">🚀</span>
            <span class="emoji-option">🌈</span>
            <span class="emoji-option">🍕</span>
            <span class="emoji-option">🐱</span>
            <span class="emoji-option">🐶</span>
            <span class="emoji-option">🦄</span>
            <span class="emoji-option">🌺</span>
            <span class="emoji-option">☕</span>
            <span class="emoji-option">🎵</span>
            <span class="emoji-option">📚</span>
            <span class="emoji-option">✨</span>
            <span class="emoji-option">🎨</span>
            <span class="emoji-option">⭐</span>
            <span class="emoji-option">💖</span>
            <span class="emoji-option">💯</span>
            <span class="emoji-option">🏆</span>
            <span class="emoji-option">🎁</span>
            <span class="emoji-option">🌞</span>
            <span class="emoji-option">🌙</span>
            <span class="emoji-option">⚡</span>
            <span class="emoji-option">🌊</span>
            <span class="emoji-option">🔍</span>
        </div>

        <div id="shape-picker" class="emoji-picker">
            <span class="shape-option" data-shape="rectangle">□</span>
            <span class="shape-option" data-shape="circle">○</span>
            <span class="shape-option" data-shape="triangle">△</span>
            <span class="shape-option" data-shape="line">╱</span>
            <span class="shape-option" data-shape="arrow">→</span>
            <span class="shape-option" data-shape="heart">♥</span>
            <span class="shape-option" data-shape="star">★</span>
            <span class="shape-option" data-shape="diamond">◆</span>
        </div>

        <div id="text-input-container" class="text-input-container">
            <input type="text" id="text-input" placeholder="Type your text here..." class="text-input">
            <button id="add-text-button" class="tool-button">Add Text</button>
        </div>

        <div class="color-picker" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
            <div class="color-option" data-color="#000000" style="background-color: #000000;"></div>
            <div class="color-option" data-color="#FF0000" style="background-color: #FF0000;"></div>
            <div class="color-option" data-color="#FF9900" style="background-color: #FF9900;"></div>
            <div class="color-option" data-color="#FFFF00" style="background-color: #FFFF00;"></div>
            <div class="color-option" data-color="#00FF00" style="background-color: #00FF00;"></div>
            <div class="color-option" data-color="#00FFFF" style="background-color: #00FFFF;"></div>
            <div class="color-option" data-color="#0000FF" style="background-color: #0000FF;"></div>
            <div class="color-option" data-color="#9900FF" style="background-color: #9900FF;"></div>
            <div class="color-option" data-color="#FF00FF" style="background-color: #FF00FF;"></div>
            <div class="color-option" data-color="#FF99CC" style="background-color: #FF99CC;"></div>
        </div>

        <div class="size-slider-container" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
            <span class="size-slider-label">Size:</span>
            <input type="range" id="size-slider" min="1" max="20" value="5" class="size-slider">
        </div>

        <div class="canvas-container" data-aos="fade-up" data-aos-duration="800" data-aos-delay="500">
            <canvas id="boardy-canvas"></canvas>
        </div>

        <div class="clear-notice" data-aos="fade-up" data-aos-duration="800" data-aos-delay="600">
            <i class="fas fa-info-circle"></i> Your drawings are saved in your browser and will be automatically cleared after 24 hours.
        </div>
    </div>
</section>

<script src="{{ .Site.Params.staticPath }}/js/boardy-fixed.js"></script>
{{ end }}
