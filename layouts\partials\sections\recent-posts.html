{{ if .Site.Params.recentPosts.enable | default false }}
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/recent-posts-neo-cute.css">
<section id="recent-posts">
    <div class="container">
        <h3 class="text-center">{{ .Site.Params.recentPosts.title | default "Recent Posts" }}</h3>
        <div class="row justify-content-center py-5">
            {{ $pages := where site.RegularPages "Type" "in" site.Params.mainSections }}
            {{ $paginator := .Paginate ($pages) 6 }}
            {{ range first 3 $paginator.Pages }}
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                <div class="card h-100">
                    <div class="card-img-container">
                        {{ if .Params.image }}
                        <img src="{{ .Params.image }}" class="card-img" alt="{{ .Title }}">
                        {{ else }}
                        <img src="{{ .Site.Params.staticPath }}/images/default.jpg" class="card-img" alt="{{ .Title }}">
                        {{ end }}
                        <div class="post-date">{{ .Date.Format "Jan 2, 2006" }}</div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">{{ .Title }}</h5>
                        {{ if .Params.tags }}
                        <div class="post-tags">
                            {{ range .Params.tags }}
                            <span class="post-tag">{{ . }}</span>
                            {{ end }}
                        </div>
                        {{ end }}
                        <p class="card-text">{{ .Summary | truncate 120 }}</p>
                        <a href="{{ .Permalink }}" class="btn">Read More</a>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>
        <div class="text-center mt-4">
            <a href="{{ .Site.BaseURL }}blog/" class="btn">View All Posts</a>
        </div>
    </div>
</section>
{{ end }}
