/* Scroll-aware navbar styles */
.navbar-fixed {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
    transition: transform 0.3s ease-in-out, background-color 0.3s ease;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-visible {
    transform: translateY(0);
}

.navbar-hidden {
    transform: translateY(-100%);
}

/* Glass effect for navbar on all pages */
.navbar {
    background-color: rgba(255, 255, 255, 0.8) !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

.dark .navbar {
    background-color: rgba(33, 37, 41, 0.8) !important;
}

/* Add padding to body to prevent content from being hidden under the navbar */
body {
    padding-top: 80px; /* Increased padding to ensure content doesn't go behind navbar */
}

@media (min-width: 992px) {
    body {
        padding-top: 100px; /* Increased padding for larger navbar on desktop */
    }
}

/* Fix for specific page types to ensure proper spacing */
.blog-list-container,
.til-section,
.gallery-section,
.taxonomy-section,
.exercise-section,
#habit-tracker,
.blog-single,
.til-single,
.exercise-single {
    margin-top: 20px !important;
}
