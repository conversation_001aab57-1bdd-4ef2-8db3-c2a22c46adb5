/* Neo-brutalism Book Reviews Styles */

/* Book reviews container */
.book-reviews-container {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .book-reviews-container {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.book-reviews-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#B5EAD7 6px, transparent 6px),
        radial-gradient(#B5EAD7 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .book-reviews-container::before {
    background-image:
        radial-gradient(rgba(181, 234, 215, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(181, 234, 215, 0.3) 6px, transparent 6px);
}

/* Book reviews title */
.book-reviews-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #B5EAD7;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .book-reviews-title {
    color: #fff;
    background-color: rgba(181, 234, 215, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Book description */
.book-description {
    position: relative;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
}

.dark .book-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Book card */
.book-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
}

.dark .book-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.book-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .book-card:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Book card image container */
.book-card-image {
    position: relative;
    overflow: hidden;
    height: 300px;
    background-color: #f0f0f0;
    z-index: 1;
    border-bottom: 2px solid #000;
}

.dark .book-card-image {
    background-color: #333;
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

/* Book card image */
.book-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    z-index: 2;
    display: block;
}

.book-card:hover .book-card-image img {
    transform: scale(1.05);
}

/* Book card content */
.book-card-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Book card title */
.book-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .book-card-title {
    color: #fff;
}

/* Book card author */
.book-card-author {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 15px;
    font-style: italic;
}

.dark .book-card-author {
    color: rgba(255, 255, 255, 0.6);
}

/* Book card description */
.book-card-description {
    font-size: 0.9rem;
    color: #555;
    margin-bottom: 15px;
    flex-grow: 1;
}

.dark .book-card-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Book card rating */
.book-card-rating {
    margin-bottom: 15px;
}

.star {
    color: #FFD166;
    font-size: 1.2rem;
}

.dark .star {
    color: rgba(255, 209, 102, 0.7);
}

/* Book card footer */
.book-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

/* Book card tags */
.book-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 15px;
}

.book-card-tag {
    display: inline-block;
    background-color: #FFD1DC;
    color: #000;
    padding: 3px 8px;
    font-size: 0.7rem;
    font-weight: 600;
    border: 2px solid #000;
}

.dark .book-card-tag {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Book card button */
.book-card-button {
    display: inline-block;
    background-color: #A2D2FF;
    color: #000;
    padding: 8px 15px;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid #000;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .book-card-button {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.book-card-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .book-card-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Book single page */
.book-single {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 40px;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin-bottom: 30px;
    position: relative;
}

.dark .book-single {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Book single header */
.book-single-header {
    margin-bottom: 30px;
    text-align: center;
}

/* Book single title */
.book-single-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .book-single-title {
    color: #fff;
}

/* Book single meta */
.book-single-meta {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.book-single-author,
.book-single-date,
.book-single-rating {
    font-size: 0.9rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dark .book-single-author,
.dark .book-single-date,
.dark .book-single-rating {
    color: rgba(255, 255, 255, 0.6);
}

/* Book single image */
.book-single-image {
    float: left;
    width: 200px;
    height: 300px;
    margin-right: 30px;
    margin-bottom: 20px;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    overflow: hidden;
}

.dark .book-single-image {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.book-single-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Book single content */
.book-single-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.dark .book-single-content {
    color: rgba(255, 255, 255, 0.9);
}

.book-single-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-top: 30px;
    margin-bottom: 15px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .book-single-content h2 {
    color: #fff;
}

.book-single-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 25px;
    margin-bottom: 15px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .book-single-content h3 {
    color: #fff;
}

.book-single-content p {
    margin-bottom: 20px;
}

.book-single-content blockquote {
    border-left: 5px solid #B5EAD7;
    padding-left: 20px;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
    color: #555;
}

.dark .book-single-content blockquote {
    border-left: 5px solid rgba(181, 234, 215, 0.5);
    color: rgba(255, 255, 255, 0.7);
}

/* Book single tags */
.book-single-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 30px;
}

.book-single-tag {
    display: inline-block;
    background-color: #FFD1DC;
    color: #000;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid #000;
    transition: all 0.3s ease;
}

.dark .book-single-tag {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.book-single-tag:hover {
    transform: translate(-2px, -2px);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .book-single-tag:hover {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Book single buttons */
.book-single-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 30px;
}

.book-single-button {
    display: inline-block;
    background-color: #A2D2FF;
    color: #000;
    padding: 10px 20px;
    font-weight: 600;
    text-decoration: none;
    border: 2px solid #000;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .book-single-button {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

.book-single-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .book-single-button:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Add book review button */
.add-review-button {
    display: inline-block;
    background-color: #B5EAD7;
    color: #000;
    padding: 12px 25px;
    font-weight: 700;
    text-decoration: none;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-top: 30px;
}

.dark .add-review-button {
    background-color: rgba(181, 234, 215, 0.3);
    color: #fff;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.add-review-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .add-review-button:hover {
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Responsive design */
@media (max-width: 991px) {
    .book-reviews-container {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .book-single {
        padding: 30px;
        margin: 30px 15px;
    }

    .book-single-title {
        font-size: 2rem;
    }

    .book-single-image {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .book-reviews-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .book-card-title {
        font-size: 1.1rem;
    }

    .book-card-content {
        padding: 15px;
    }

    .book-single-content {
        font-size: 1rem;
    }

    .book-single-content h2 {
        font-size: 1.5rem;
    }

    .book-single-content h3 {
        font-size: 1.3rem;
    }

    .book-single-image {
        width: 200px;
    }
}

@media (max-width: 576px) {
    .book-reviews-container {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .book-single {
        padding: 20px;
        margin: 20px 10px;
    }

    .book-single-title {
        font-size: 1.8rem;
    }

    .book-single-meta {
        flex-direction: column;
        gap: 10px;
    }

    .book-single-image {
        float: none;
        width: 100%;
        max-width: 300px;
        margin: 0 auto 20px;
    }
}
