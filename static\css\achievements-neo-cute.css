/* Neo-brutalism Achievements Section */
#achievements {
    background-color: var(--background-color);
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    padding: 30px 20px;
    position: relative;
    overflow: hidden;
}

.dark #achievements {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Decorative elements */
#achievements::before {
    content: "★";
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 40px;
    color: #FFD166;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(15deg);
}

.dark #achievements::before {
    color: rgba(255, 209, 102, 0.7);
}

#achievements::after {
    content: "✓";
    position: absolute;
    bottom: 20px;
    left: 30px;
    font-size: 40px;
    color: #A2D2FF;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(-15deg);
}

.dark #achievements::after {
    color: rgba(162, 210, 255, 0.7);
}

/* Section title */
#achievements h3 {
    margin-bottom: 2.5rem;
    position: relative;
    font-weight: 700;
    color: var(--text-color);
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 3px solid #000;
    z-index: 1;
}

.dark #achievements h3 {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Achievement cards */
#achievements .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.dark #achievements .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

#achievements .card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
}

.dark #achievements .card:hover {
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Card image */
#achievements .card-head {
    overflow: hidden;
    border-bottom: 3px solid #000;
    margin: -1rem -1rem 1rem -1rem;
}

.dark #achievements .card-head {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

#achievements .card-img-top {
    transition: transform 0.5s ease;
    width: 100%;
    height: auto;
}

#achievements .card:hover .card-img-top {
    transform: scale(1.05);
}

/* Card content */
#achievements .card-title {
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    position: relative;
}

#achievements .card-title::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #FFD166;
}

.dark #achievements .card-title::after {
    background-color: rgba(255, 209, 102, 0.7);
}

#achievements .card-text {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #achievements {
        margin: 30px 15px;
        padding: 25px 15px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }
    
    #achievements .card {
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements .card {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #achievements .card:hover {
        transform: translate(-3px, -3px);
        box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements .card:hover {
        box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 480px) {
    #achievements {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #achievements h3 {
        font-size: 1.5rem;
    }
    
    #achievements .card {
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements .card {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
    
    #achievements .card:hover {
        transform: translate(-2px, -2px);
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #achievements .card:hover {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
}
