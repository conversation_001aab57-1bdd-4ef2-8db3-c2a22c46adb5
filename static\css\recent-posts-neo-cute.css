/* Neo-brutalism Recent Posts Section */
#recent-posts {
    background-color: var(--background-color);
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    padding: 30px 20px;
    position: relative;
    overflow: hidden;
}

.dark #recent-posts {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Decorative elements */
#recent-posts::before {
    content: "📝";
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(15deg);
}

#recent-posts::after {
    content: "📰";
    position: absolute;
    bottom: 20px;
    left: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(-15deg);
}

/* Section title */
#recent-posts h3 {
    margin-bottom: 2.5rem;
    position: relative;
    font-weight: 700;
    color: var(--text-color);
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 3px solid #000;
    z-index: 1;
}

.dark #recent-posts h3 {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Post cards */
#recent-posts .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.dark #recent-posts .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

#recent-posts .card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
}

.dark #recent-posts .card:hover {
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Card image */
#recent-posts .card-img-container {
    overflow: hidden;
    border-bottom: 3px solid #000;
    position: relative;
}

.dark #recent-posts .card-img-container {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

#recent-posts .card-img {
    transition: transform 0.5s ease;
    width: 100%;
    height: 200px;
    object-fit: cover;
}

#recent-posts .card:hover .card-img {
    transform: scale(1.05);
}

/* Card content */
#recent-posts .card-body {
    padding: 1.5rem;
}

#recent-posts .card-title {
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    position: relative;
}

#recent-posts .card-title::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #FFD166;
}

.dark #recent-posts .card-title::after {
    background-color: rgba(255, 209, 102, 0.7);
}

#recent-posts .card-text {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Post date */
#recent-posts .post-date {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #FFD1DC;
    color: #000;
    padding: 5px 10px;
    border: 2px solid #000;
    font-weight: 600;
    font-size: 0.8rem;
    z-index: 1;
}

.dark #recent-posts .post-date {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Read more button */
#recent-posts .btn {
    background-color: #A2D2FF;
    color: #000;
    font-weight: 600;
    padding: 8px 15px;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark #recent-posts .btn {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

#recent-posts .btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark #recent-posts .btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Tags */
#recent-posts .post-tags {
    margin-bottom: 1rem;
}

#recent-posts .post-tag {
    display: inline-block;
    background-color: #B5EAD7;
    color: #000;
    padding: 2px 8px;
    margin-right: 5px;
    margin-bottom: 5px;
    border: 2px solid #000;
    font-size: 0.8rem;
    font-weight: 600;
}

.dark #recent-posts .post-tag {
    background-color: rgba(181, 234, 215, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #recent-posts {
        margin: 30px 15px;
        padding: 25px 15px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }
    
    #recent-posts .card {
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts .card {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #recent-posts .card:hover {
        transform: translate(-3px, -3px);
        box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts .card:hover {
        box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    }
    
    #recent-posts .card-body {
        padding: 1rem;
    }
    
    #recent-posts .card-img {
        height: 150px;
    }
}

@media (max-width: 480px) {
    #recent-posts {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #recent-posts h3 {
        font-size: 1.5rem;
    }
    
    #recent-posts .card {
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts .card {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
    
    #recent-posts .card:hover {
        transform: translate(-2px, -2px);
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #recent-posts .card:hover {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
}
