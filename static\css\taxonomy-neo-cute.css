/* Neo-brutalism Taxonomy (Tags & Categories) Pages with Cute Elements */

/* Taxonomy section container */
.taxonomy-section {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .taxonomy-section {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.taxonomy-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .taxonomy-section::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Section title */
.taxonomy-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .taxonomy-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Taxonomy description */
.taxonomy-description {
    position: relative;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    z-index: 1;
}

.dark .taxonomy-description {
    color: rgba(255, 255, 255, 0.9);
}

/* Taxonomy list container */
.taxonomy-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
}

/* Taxonomy item */
.taxonomy-item {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    text-decoration: none;
    color: #333;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.dark .taxonomy-item {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
}

.taxonomy-item:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .taxonomy-item:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Taxonomy count */
.taxonomy-count {
    background-color: #FFD1DC;
    color: #000;
    border: 1.5px solid #000;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 700;
}

.dark .taxonomy-count {
    background-color: rgba(255, 209, 220, 0.3);
    border: 1.5px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

/* Taxonomy icon */
.taxonomy-icon {
    color: #FF6B8B;
    font-size: 1rem;
}

.dark .taxonomy-icon {
    color: rgba(255, 107, 139, 0.7);
}

/* Post card container */
.post-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    position: relative;
    z-index: 1;
}

/* Post card */
.post-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.dark .post-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.post-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .post-card:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Source indicator */
.post-card::before {
    content: attr(data-source);
    position: absolute;
    top: 0;
    right: 0;
    padding: 5px 10px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
    text-transform: uppercase;
}

/* Source colors */
.post-card[data-source="blog"]::before {
    background-color: #FFD1DC;
    color: #000;
    border-bottom-left-radius: 8px;
    border-left: 2px solid #000;
    border-bottom: 2px solid #000;
}

.dark .post-card[data-source="blog"]::before {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border-left: 2px solid rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.post-card[data-source="til"]::before {
    background-color: #A2D2FF;
    color: #000;
    border-bottom-left-radius: 8px;
    border-left: 2px solid #000;
    border-bottom: 2px solid #000;
}

.dark .post-card[data-source="til"]::before {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border-left: 2px solid rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.post-card[data-source="exercise"]::before {
    background-color: #B5EAD7;
    color: #000;
    border-bottom-left-radius: 8px;
    border-left: 2px solid #000;
    border-bottom: 2px solid #000;
}

.dark .post-card[data-source="exercise"]::before {
    background-color: rgba(181, 234, 215, 0.3);
    color: #fff;
    border-left: 2px solid rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.post-card[data-source="habit"]::before {
    background-color: #C7CEEA;
    color: #000;
    border-bottom-left-radius: 8px;
    border-left: 2px solid #000;
    border-bottom: 2px solid #000;
}

.dark .post-card[data-source="habit"]::before {
    background-color: rgba(199, 206, 234, 0.3);
    color: #fff;
    border-left: 2px solid rgba(255, 255, 255, 0.7);
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

/* Post card image container */
.post-card-image {
    position: relative;
    overflow: hidden;
    padding-top: 60%;
}

/* Post card image */
.post-card-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    border-bottom: 2px solid #000;
}

.dark .post-card-image img {
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.post-card:hover .post-card-image img {
    transform: scale(1.05);
}

/* Post card content */
.post-card-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Post card title */
.post-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .post-card-title {
    color: #fff;
}

/* Post card description */
.post-card-description {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 15px;
    flex-grow: 1;
}

.dark .post-card-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Post card meta */
.post-card-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #777;
    margin-top: auto;
    border-top: 1px dashed #ccc;
    padding-top: 15px;
}

.dark .post-card-meta {
    color: rgba(255, 255, 255, 0.6);
    border-top: 1px dashed rgba(255, 255, 255, 0.3);
}

/* Post card date */
.post-card-date {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* Post card read more */
.post-card-read {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
}

.dark .post-card-read {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.post-card-read:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .post-card-read:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Cute decorative elements */
.taxonomy-section::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .taxonomy-section::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    margin: 20px 0;
    position: relative;
    z-index: 1;
}

.dark .empty-state {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.empty-state-icon {
    font-size: 3rem;
    color: #FFD1DC;
    margin-bottom: 20px;
}

.dark .empty-state-icon {
    color: rgba(255, 209, 220, 0.5);
}

.empty-state-text {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 20px;
}

.dark .empty-state-text {
    color: rgba(255, 255, 255, 0.7);
}

/* Back button */
.back-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.dark .back-button {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.back-button:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .back-button:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Responsive design */
@media (max-width: 991px) {
    .taxonomy-section {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .post-cards {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .taxonomy-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .taxonomy-description {
        font-size: 1rem;
    }

    .post-card-title {
        font-size: 1.1rem;
    }

    .post-card-content {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .taxonomy-section {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .post-cards {
        grid-template-columns: 1fr;
    }

    .taxonomy-list {
        gap: 10px;
    }

    .taxonomy-item {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

/* Single tag/category page specific styles */
.taxonomy-header {
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
}

.taxonomy-header-title {
    display: inline-block;
    font-size: 2rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 10px;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    font-family: 'Playfair Display', serif;
}

.dark .taxonomy-header-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.taxonomy-header-count {
    display: inline-block;
    margin-left: 15px;
    font-size: 1.2rem;
    color: #555;
}

.dark .taxonomy-header-count {
    color: rgba(255, 255, 255, 0.7);
}

/* Pagination */
.pagination-container {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.page-item {
    display: inline-block;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .page-link {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.page-link:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .page-link:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.page-item.active .page-link {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
}

.dark .page-item.active .page-link {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Tag cloud specific styles */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin: 30px 0;
    position: relative;
    z-index: 1;
}

.tag-cloud-tag {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    text-decoration: none;
    color: #333;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: var(--tag-size, 1rem);
}

.dark .tag-cloud-tag {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
}

.tag-cloud-tag:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .tag-cloud-tag:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Tag in single post */
.post-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.post-tag {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
}

.dark .post-tag {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.post-tag:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .post-tag:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.post-tag-icon {
    font-size: 0.8rem;
}
