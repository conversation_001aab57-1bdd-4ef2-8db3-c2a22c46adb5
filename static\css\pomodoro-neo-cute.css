/* Neo-brutalism Pomodoro Timer with Cute Elements */

/* Pomodoro section container */
.pomodoro-section {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .pomodoro-section {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.pomodoro-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .pomodoro-section::before {
    background-image: 
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Section title */
.pomodoro-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .pomodoro-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Pomodoro description */
.pomodoro-description {
    position: relative;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    z-index: 1;
}

.dark .pomodoro-description {
    color: rgba(255, 255, 255, 0.9);
}

/* Pomodoro card */
.pomodoro-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
}

.dark .pomodoro-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Pomodoro card header */
.pomodoro-card-header {
    background-color: #FFD1DC;
    padding: 15px;
    border-bottom: 3px solid #000;
}

.dark .pomodoro-card-header {
    background-color: rgba(255, 209, 220, 0.3);
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Pomodoro mode selector */
.pomodoro-mode-selector {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.pomodoro-mode-btn {
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .pomodoro-mode-btn {
    background-color: #2a2a2a;
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.pomodoro-mode-btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .pomodoro-mode-btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.pomodoro-mode-btn.active {
    background-color: #A2D2FF;
    color: #000;
}

.dark .pomodoro-mode-btn.active {
    background-color: rgba(162, 210, 255, 0.5);
    color: #fff;
}

/* Pomodoro card body */
.pomodoro-card-body {
    padding: 30px;
}

/* Pomodoro timer */
.pomodoro-timer {
    text-align: center;
    margin-bottom: 30px;
}

.pomodoro-timer-display {
    font-size: 5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    font-family: 'Courier New', monospace;
    background-color: #f5f5f5;
    padding: 10px 20px;
    display: inline-block;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .pomodoro-timer-display {
    color: #fff;
    background-color: #333;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.pomodoro-timer-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.pomodoro-btn {
    padding: 10px 20px;
    font-weight: 600;
    border: 2px solid #000;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
}

.dark .pomodoro-btn {
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

.pomodoro-btn:hover:not(:disabled) {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .pomodoro-btn:hover:not(:disabled) {
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.pomodoro-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pomodoro-btn.primary {
    background-color: #A2D2FF;
    color: #000;
}

.dark .pomodoro-btn.primary {
    background-color: rgba(162, 210, 255, 0.5);
    color: #fff;
}

.pomodoro-btn.secondary {
    background-color: #FFD1DC;
    color: #000;
}

.dark .pomodoro-btn.secondary {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
}

.pomodoro-btn.danger {
    background-color: #FF6B6B;
    color: #000;
}

.dark .pomodoro-btn.danger {
    background-color: rgba(255, 107, 107, 0.5);
    color: #fff;
}

/* Pomodoro settings */
.pomodoro-settings {
    margin-top: 30px;
    border-top: 2px dashed #ccc;
    padding-top: 20px;
}

.dark .pomodoro-settings {
    border-top: 2px dashed rgba(255, 255, 255, 0.3);
}

.pomodoro-settings-group {
    margin-bottom: 20px;
}

.pomodoro-settings-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #333;
}

.dark .pomodoro-settings-group label {
    color: rgba(255, 255, 255, 0.9);
}

.pomodoro-range {
    width: 100%;
    height: 10px;
    -webkit-appearance: none;
    appearance: none;
    background: #f0f0f0;
    outline: none;
    border-radius: 0;
    border: 2px solid #000;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
}

.dark .pomodoro-range {
    background: #444;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
}

.pomodoro-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #FFD1DC;
    cursor: pointer;
    border: 2px solid #000;
    border-radius: 0;
}

.dark .pomodoro-range::-webkit-slider-thumb {
    background: rgba(255, 209, 220, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.pomodoro-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #FFD1DC;
    cursor: pointer;
    border: 2px solid #000;
    border-radius: 0;
}

.dark .pomodoro-range::-moz-range-thumb {
    background: rgba(255, 209, 220, 0.7);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Ambient sounds card */
.ambient-sounds-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin-bottom: 30px;
    position: relative;
    z-index: 1;
    padding: 20px;
}

.dark .ambient-sounds-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

.ambient-sounds-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px dashed #ccc;
}

.dark .ambient-sounds-title {
    color: #fff;
    border-bottom: 2px dashed rgba(255, 255, 255, 0.3);
}

.ambient-sounds-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.ambient-sound-item {
    background-color: #f5f5f5;
    border: 2px solid #000;
    border-radius: 0;
    padding: 15px;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.dark .ambient-sound-item {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.1);
}

.ambient-sound-item:hover {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.5);
}

.dark .ambient-sound-item:hover {
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.1);
}

.ambient-sound-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.ambient-sound-name {
    font-weight: 600;
    color: #333;
}

.dark .ambient-sound-name {
    color: rgba(255, 255, 255, 0.9);
}

.ambient-sound-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ambient-sound-btn {
    width: 30px;
    height: 30px;
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.5);
}

.dark .ambient-sound-btn {
    background-color: rgba(162, 210, 255, 0.5);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.1);
}

.ambient-sound-btn:hover {
    transform: translate(-1px, -1px);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.5);
}

.dark .ambient-sound-btn:hover {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
}

.ambient-sound-btn.playing {
    background-color: #FFD1DC;
}

.dark .ambient-sound-btn.playing {
    background-color: rgba(255, 209, 220, 0.5);
}

.ambient-sound-volume {
    flex: 1;
    height: 6px;
    -webkit-appearance: none;
    appearance: none;
    background: #ddd;
    outline: none;
    border-radius: 0;
    border: 1px solid #000;
}

.dark .ambient-sound-volume {
    background: #444;
    border: 1px solid rgba(255, 255, 255, 0.7);
}

.ambient-sound-volume::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    background: #FFD1DC;
    cursor: pointer;
    border: 1px solid #000;
    border-radius: 0;
}

.dark .ambient-sound-volume::-webkit-slider-thumb {
    background: rgba(255, 209, 220, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.7);
}

.ambient-sound-volume::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #FFD1DC;
    cursor: pointer;
    border: 1px solid #000;
    border-radius: 0;
}

.dark .ambient-sound-volume::-moz-range-thumb {
    background: rgba(255, 209, 220, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.7);
}

/* Cute decorative elements */
.pomodoro-section::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .pomodoro-section::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Responsive design */
@media (max-width: 991px) {
    .pomodoro-section {
        margin: 30px 15px;
        padding: 40px 0;
    }
    
    .pomodoro-timer-display {
        font-size: 4rem;
    }
    
    .ambient-sounds-container {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    }
}

@media (max-width: 768px) {
    .pomodoro-title {
        font-size: 1.8rem;
        padding: 8px 15px;
    }
    
    .pomodoro-description {
        font-size: 1rem;
    }
    
    .pomodoro-timer-display {
        font-size: 3.5rem;
    }
    
    .pomodoro-card-body {
        padding: 20px;
    }
    
    .ambient-sounds-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 576px) {
    .pomodoro-section {
        margin: 20px 10px;
        padding: 30px 0;
    }
    
    .pomodoro-title {
        font-size: 1.5rem;
        padding: 6px 12px;
    }
    
    .pomodoro-timer-display {
        font-size: 3rem;
    }
    
    .pomodoro-timer-controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .ambient-sounds-container {
        grid-template-columns: 1fr;
    }
}
