{{ define "head" }}
<meta name="description" content="{{ if .Params.description }}{{ .Params.description }}{{ else }}{{ .Summary }}{{ end }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/til-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
{{ .Title }} | Today I Learned | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<div class="container">
    <div class="til-single">
        <div class="til-single-header">
            <h1 class="til-single-title">{{ .Title }}</h1>
            <div class="til-single-meta">
                <div class="til-single-date">
                    <i class="far fa-calendar-alt"></i>
                    {{ .Date.Format "January 2, 2006" }}
                </div>
                {{ if .Params.category }}
                <div class="til-single-category">
                    <i class="fas fa-folder"></i>
                    {{ .Params.category }}
                </div>
                {{ end }}
            </div>
        </div>

        <div class="til-single-content">
            {{ .Content }}
        </div>

        {{ if .Params.tags }}
        <div class="til-single-tags">
            {{ range .Params.tags }}
            <a href="{{ "tags" | absURL }}/{{ . | urlize }}" class="til-single-tag">
                <i class="fas fa-tag"></i>
                {{ . }}
            </a>
            {{ end }}
        </div>
        {{ end }}

        <div class="til-single-nav">
            {{ if .PrevInSection }}
            <a href="{{ .PrevInSection.Permalink }}" class="til-single-nav-link prev">
                <i class="fas fa-arrow-left"></i>
                Previous
            </a>
            {{ else }}
            <span class="til-single-nav-link prev disabled">
                <i class="fas fa-arrow-left"></i>
                Previous
            </span>
            {{ end }}

            <a href="{{ "til" | absURL }}" class="til-single-nav-link">
                <i class="fas fa-th-list"></i>
                All TILs
            </a>

            {{ if .NextInSection }}
            <a href="{{ .NextInSection.Permalink }}" class="til-single-nav-link next">
                Next
                <i class="fas fa-arrow-right"></i>
            </a>
            {{ else }}
            <span class="til-single-nav-link next disabled">
                Next
                <i class="fas fa-arrow-right"></i>
            </span>
            {{ end }}
        </div>
    </div>
</div>
{{ end }}
