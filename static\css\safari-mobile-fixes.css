/* Safari and Mobile Compatibility Fixes */

/* Safari-specific fixes */
@supports (-webkit-appearance: none) {
    /* Custom cursor fallback for Safari */
    html, body, *, *::before, *::after {
        cursor: auto !important;
    }
    
    /* Safari-specific cursor for interactive elements */
    button, a, input, select, textarea, .dressup-item, .game-cell, .character-container,
    .dressup-tab, .dressup-button, .home-button, .dressup-random, .dressup-reset,
    #closeDressUpButton, #resetButton, #playButton, #dressUpButton,
    .dressup-items, .dressup-category, .dressup-options, .dressup-preview,
    .dressup-content, .dressup-container, .error-container, .error-page {
        cursor: pointer !important;
    }
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
    /* Reduce motion for better performance */
    @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* Hardware acceleration for animations */
    .error-page,
    .gallery-item,
    .gallery-item-inner,
    .gallery-image,
    .character-container,
    .lightbox,
    .lightbox-content {
        -webkit-transform: translateZ(0);
        transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        perspective: 1000px;
        will-change: transform;
    }
    
    /* Optimize touch interactions */
    .gallery-item,
    .gallery-item-inner,
    .filter-button,
    .home-button,
    .character-container {
        touch-action: manipulation;
        -webkit-tap-highlight-color: transparent;
    }
    
    /* Reduce complex animations on mobile */
    .error-page {
        animation: none;
        background-image: none;
        background-color: #fffef0;
    }
    
    .dark .error-page {
        background-color: #1a1a1a;
        background-image: none;
    }
    
    /* Simplify gallery animations */
    .gallery-item {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    .gallery-item:hover .gallery-item-inner {
        transform: translate(-2px, -2px);
    }
    
    .gallery-item:hover .gallery-image {
        transform: scale(1.02);
    }
    
    /* Optimize confetti animation */
    .confetti {
        animation-duration: 2s;
        will-change: transform, opacity;
    }
    
    /* Reduce parallax effects on mobile */
    .hero-image,
    .tech-circle {
        transform: none !important;
    }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    /* Fix for iOS Safari viewport issues */
    .error-page,
    .gallery-section {
        min-height: -webkit-fill-available;
    }
    
    /* Fix for iOS Safari scroll issues */
    body {
        -webkit-overflow-scrolling: touch;
    }
    
    /* Fix for iOS Safari button styling */
    button,
    .btn,
    .home-button,
    .filter-button {
        -webkit-appearance: none;
        border-radius: 0;
    }
    
    /* Fix for iOS Safari input zoom */
    input, select, textarea {
        font-size: 16px;
    }
}

/* Safari animation optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
    /* Optimize keyframe animations for Safari */
    @keyframes gradientBackground {
        0% {
            background-position: 0% 50%;
        }
        50% {
            background-position: 100% 50%;
        }
        100% {
            background-position: 0% 50%;
        }
    }
    
    @keyframes confetti-fall {
        0% {
            transform: translate3d(0, -50px, 0) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translate3d(0, 500px, 0) rotate(360deg);
            opacity: 0;
        }
    }
    
    @keyframes fadeInUp {
        0% {
            opacity: 0;
            transform: translate3d(0, 20px, 0);
        }
        100% {
            opacity: 1;
            transform: translate3d(0, 0, 0);
        }
    }
}

/* Fix for Safari flexbox issues */
.error-buttons,
.gallery-filters,
.dressup-tabs {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
}

/* Fix for Safari grid issues */
.gallery-grid {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: repeat(auto-fill, minmax(250px, 1fr));
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Mobile-specific gallery optimizations */
@media (max-width: 576px) {
    .gallery-grid {
        -ms-grid-columns: repeat(auto-fill, minmax(150px, 1fr));
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 8px;
    }
    
    .gallery-item-inner {
        transition: transform 0.2s ease;
    }
    
    .gallery-item:hover .gallery-item-inner {
        transform: translate(-1px, -1px);
    }
}

/* Fix for Safari transform issues */
.character-container,
.gallery-item-inner,
.lightbox-content {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d;
}

/* Optimize scroll performance */
.lightbox,
.error-page,
.gallery-section {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
}

/* Fix for Safari backdrop-filter support */
@supports (backdrop-filter: blur(10px)) {
    .lightbox {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
}

@supports not (backdrop-filter: blur(10px)) {
    .lightbox {
        background-color: rgba(0, 0, 0, 0.9);
    }
}

/* Performance optimizations for older devices */
@media (max-width: 768px) and (max-resolution: 150dpi) {
    /* Reduce visual effects for lower-end devices */
    .gallery-item-inner,
    .character-container,
    .home-button {
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .gallery-item-inner,
    .dark .character-container,
    .dark .home-button {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
    
    /* Simplify hover effects */
    .gallery-item:hover .gallery-item-inner {
        transform: none;
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .gallery-item:hover .gallery-item-inner {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
}

/* Fix for Safari position sticky issues */
.navbar {
    position: -webkit-sticky;
    position: sticky;
}

/* Optimize font rendering */
body, html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
    /* Further reduce animations on very small screens */
    .gallery-item,
    .character-container,
    .error-page {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }

    /* Simplify shadows for better performance */
    .gallery-item-inner,
    .home-button {
        box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7) !important;
    }

    .dark .gallery-item-inner,
    .dark .home-button {
        box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.15) !important;
    }
}

/* CSS custom properties for dynamic viewport height (iOS Safari fix) */
:root {
    --vh: 1vh;
}

.error-page,
.gallery-section {
    min-height: calc(var(--vh, 1vh) * 100);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .error-page {
        background-image: none !important;
        animation: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gallery-item-inner,
    .home-button {
        border-width: 4px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 1);
    }

    .dark .gallery-item-inner,
    .dark .home-button {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 1);
    }
}
