{{ define "head" }}
<meta name="description" content="{{ if .Params.description }}{{ .Params.description }}{{ else }}{{ .Summary }}{{ end }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/book-reviews-neo-cute.css" media="all">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/table-neo-cute.css" media="all">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/toc-neo.css" media="all">
<!-- fontawesome -->
<script defer src="{{ .Site.Params.staticPath }}/fontawesome-5/all-5.15.4.js"></script>
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-9 col-md-12">
            <div class="book-single">
                <div class="book-single-header">
                    <h1 class="book-single-title">{{ .Title }}</h1>
                    <div class="book-single-meta">
                        {{ if .Params.author }}
                        <div class="book-single-author">
                            <i class="fas fa-user"></i>
                            {{ .Params.author }}
                        </div>
                        {{ end }}
                        <div class="book-single-date">
                            <i class="far fa-calendar-alt"></i>
                            {{ .Date.Format (.Site.Params.datesFormat.article | default "Jan 2, 2006") }}
                        </div>
                        {{ if .Params.rating }}
                        <div class="book-single-rating">
                            <i class="fas fa-star"></i>
                            {{ $rating := int .Params.rating }}
                            {{ range $i, $e := (seq 5) }}
                                {{ if le $i $rating }}
                                <span class="star">★</span>
                                {{ else }}
                                <span class="star" style="opacity: 0.3;">★</span>
                                {{ end }}
                            {{ end }}
                        </div>
                        {{ end }}
                    </div>
                </div>

                {{ if .Params.image }}
                <div class="book-single-image">
                    <img src="{{ .Params.image }}" alt="{{ .Title }}">
                </div>
                {{ end }}

                <div class="book-single-content">
                    {{ .Content | emojify }}

                    {{ if .Params.buttons }}
                    <div class="book-single-buttons">
                        {{ range .Params.buttons }}
                        <a href="{{ .url }}" class="book-single-button" {{ if .newTab }}target="_blank"{{ end }}>{{ .label }}</a>
                        {{ end }}
                    </div>
                    {{ end }}
                </div>

                {{ if .Params.tags }}
                <div class="book-single-tags">
                    {{ range .Params.tags }}
                    <a href="{{ "tags" | absURL }}/{{ . | urlize }}" class="book-single-tag">
                        <i class="fas fa-tag"></i>
                        {{ . }}
                    </a>
                    {{ end }}
                </div>
                {{ end }}
            </div>
        </div>
    </div>
</div>

<button onclick="topFunction()" id="topScroll" title="Go to top">
    <i class="fas fa-arrow-up"></i>
</button>

<script>
    var topScroll = document.getElementById("topScroll");
    window.onscroll = function() {scrollFunction()};

    function scrollFunction() {
        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            topScroll.style.display = "block";
        } else {
            topScroll.style.display = "none";
        }
    }

    function topFunction() {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    }
</script>
{{ end }}
