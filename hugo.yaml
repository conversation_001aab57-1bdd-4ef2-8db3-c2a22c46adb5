baseURL: "https://shreya.pages.dev/"
languageCode: "en-us"
title: "Shreya Ghimire"

outputs:
  home:
    - "HTML"
    - "RSS"
    - "JSON"
  page:
    - "HTML"
    - "RSS"

pagination:
  disableAliases: false
  pagerSize: 10
  path: page

enableRobotsTXT: true
disqusShortname: shreya-pages-dev
# googleAnalytics: G-MEASUREMENT_ID

markup:
  goldmark:
    renderer:
      unsafe: true

Menus:
  main:
    - identifier: blog
      name: Blog
      title: Blog posts
      weight: 1
    - identifier: blogs
      name: All blogs
      title: All Blog posts
      weight: 1
      parent: blog
      url: /blogs
    - identifier: tags
      name: Tags
      title: Categorical distribution of Blogs
      weight: 2
      parent: blog
      url: /Tags
    - identifier: productive
      name: Productivity
      title: I'll help you get productive
      weight: 2
    - identifier: til
      name: Today I Learned
      title: Today I Learned
      weight: 1
      parent: productive
      url: /til
    - identifier: daily-planner
      name: Daily Planner
      title: Organize Your Day
      weight: 5
      parent: productive
      url: /daily-planner
    - identifier: pomodoro
      name: Pomodoro Timer
      title: Focus with Pomodoro
      weight: 2
      parent: productive
      url: /pomodoro
    - identifier: exercise
      name: Exercices
      title: Let's Exercise Together
      weight: 4
      parent: productive
      url: /exercises/
    - identifier: habits
      name: Habit Tracker
      title: Track your habits
      weight: 3
      parent: productive
      url: /habits
    - identifier: book-reviews
      name: Book Reviews
      title: Book Reviews and Recommendations
      weight: 6
      parent: productive
      url: /book-reviews
    - identifier: toolies
      name: Toolies
      title: Simple Useful Tools
      weight: 7
      parent: productive
      url: /toolies
    - identifier: boardy
      name: Boardy
      title: Simple Whiteboard for Ideas
      weight: 8
      parent: productive
      url: /boardy

    - identifier: gallery
      name: Gallery
      title: My gallery
      url: /gallery
      weight: 3


    # Dropdown menu
    # - identifier: dropdown
    #   title: Example dropdown menu
    #   name: Dropdown
    #   weight: 3
    # - identifier: dropdown1
    #   title: example dropdown 1
    #   name: example 1
    #   url: /#
    #   parent: dropdown
    #   weight: 1
    # - identifier: dropdown2
    #   title: example dropdown 2
    #   name: example 2
    #   url: /#
    #   parent: dropdown
    #   weight: 2

params:
  title: "Shreya Ghimire"
  description: This is my personal portfolio website. You are Welcome Here. Here, I share my experiences and try to be useful to others by helping others learn what I have learned by teaching them.
  # staticPath: ""  # The path to serve the static files from
  favicon: "/fav.png"

  # Whether to serve bootstrap css and js files from CDN or not. Can be set to true, "css" or "js" to choose between
  # serving both, only the css, or only the js files through the CDN. Any other value will make so that CDN is not used.
  # Note the lack of "" in true, it should be of boolean type.
  useBootstrapCDN: false


  habitTracker:
    enable: true
    apiEndpoint: "https://catfact.ninja/fact?max_length=12"
    tokenSymbol: "#"
  # If you want to load dynamically responsive images from Cloudinary
  # This requires your images to be uploaded + hosted on Cloudinary
  # Uncomment and change YOUR_CLOUD_NAME to the Cloud Name in your Cloudinary console
  # cloudinary_cloud_name: "YOUR_CLOUD_NAME"

  # Whether the fade animations on the home page will be enabled
  animate: true

  # Preloader configuration
  preloader:
    enable: true # Set to false to disable the preloader

  # Pomodoro configuration
  pomodoro:
    enable: true
    ambientSounds:
      - name: "Rain"
        file: "/sounds/rain.mp3"
      - name: "Forest"
        file: "/sounds/forest.mp3"
      - name: "Ocean Waves"
        file: "/sounds/ocean.mp3"
      - name: "Fireplace"
        file: "/sounds/fireplace.mp3"

  theme:
    # disableThemeToggle: true
    #defaultTheme: "light" # dark

  font:
    fontSize: 1rem # default: 1rem
    fontWeight: 400 # default: 400
    lineHeight: 1.5 # default: 1.5
    textAlign: left # default: left

  # color preference
  # color:
  #   textColor:
  #   secondaryTextColor:
  #   backgroundColor:
  #   secondaryBackgroundColor:
  #   primaryColor:
  #   secondaryColor:

  #   darkmode:
  #     textColor:
  #     secondaryTextColor:
  #     backgroundColor:
  #     secondaryBackgroundColor:
  #     primaryColor:
  #     secondaryColor:

  # If you want to customize the menu, you can change it here
  navbar:
    align: ms-auto # Left: ms-auto | center: mx-auto | right: me-auto | Default: ms-auto
    #brandLogo: "/images/logo.png" # Logo for the brand | default is the favicon variable
    showBrandLogo: true # Show brand logo in nav bar | default is true
    brandName: "Shreya" # Brand name for the brand | default is the title variable
    disableSearch: false
    # searchPlaceholder: "Search"
    menus:
      disableAbout: false
      disableExperience: false
      disableEducation: false
      disableProjects: false
      disableAchievements: false
      disableContact: false

  # Hero
  hero:
    enable: true
    intro: "Hi, this is"
    title: "Shreya Ghimire"
    subtitle: "This is subtitle"
    content: "This is the content and you can add links as well here like this [LINK](https://#)"
    image: /images/hero.jpg
    typingTexts:
      - "Project Manager"
      - "Professional"
      - "Health and Peace"
    bottomImage:
      enable: true
    # roundImage: true # Make hero image circular | default false
    button:
      enable: true
      name: "Resume"
      url: "/CV/ShreyaGhimireCV.pdf"
      download: true
      newPage: false
    socialLinks:
      fontAwesomeIcons:
        - icon: fa fa-envelope
          url: mailto:<EMAIL>
        - icon: fab fa-github
          url: https://github.com/shreya
        - icon: fab fa-linkedin
          url: https://www.linkedin.com/in/shreya-ghimire
        - icon: fab fa-instagram
          url: https://t.me/

  # About
  about:
    enable: true
    title: "About Me"
    image: "/images/avatar.png"
    content: |-

    skills:
      enable: true
      title: "I possess some of these skills:"
      items:
        - name: "Project Management"
          progress: 90
        - name: "Agile & Scrum Methodologies"
          progress: 85
        - name: "Team Leadership"
          progress: 80
        - name: "Risk Management"
          progress: 75
        - name: "Stakeholder Communication"
          progress: 85
        - name: "Budget Planning & Control"
          progress: 70
        - name: "Microsoft Project"
          progress: 80
        - name: "Jira & Confluence"
          progress: 85

  # Experience
  experience:
    enable: true
    # title: "Custom Name"
    items:
      - job: "Customer Success Engineer"
        company: "Digital Network Solutions"
        companyUrl: "https://digitalnetwork.com.np/"
        date: "August 2024 - Feb 2025"
        featuredLink:
          enable: true
          name: "Know More"
          url: "/blog/hello"
        info:
          enable: true
          content: I worked as a very babal project manager.

        content: |
          I can write an entire paragraph here WOOOOOOOOO

          - **Sharad budo** is hawa.
          - **Suhesh**, is jhanai maha hawa.

      - job: "Customer Success Engineer"
        company: "Digital Network Solutions"
        companyUrl: "https://digitalnetwork.com.np/"
        date: "August 2024 - Feb 2025"
        featuredLink:
          enable: true
          name: "Know More"
          url: "/blog/hello"
        info:
          enable: true
          content: I worked as a very babal project manager.

        content: |
          I can write an entire paragraph here WOOOOOOOOO

          - **Sharad budo** is hawa.
          - **Suhesh**, is jhanai maha hawa

  # Education
  education:
    enable: true
    # title: "Custom Name"
    index: false
    items:
      - title: "Bachelors in Computer Engineering"
        school:
          name: "Birse College"
          url: "https://sajhu/"
        date: "2071 - 2073"
        content: |-
          A long paragraph
        featuredLink:
          enable: true
          name: "My Curriculam Vitae"
          url: "/CV/ShreyaGhimireCV.pdf"
      - title: "Higher Secondary Education"
        school:
          name: "Germany"
          url: "https://boooo"
        date: "2071 - 2073"
        content: |-
          A long paragraph
        featuredLink:
          enable: true
          name: "My Curriculam Vitae"
          url: "/CV/ShreyaGhimireCV.pdf"

      - title: "School Leaving Certificate"
        school:
          name: "United States of America"
          url: "https://HOOOOO"
        date: "2071"
        content: |-
          Extracurricular Activities
            - Inter School Dance Competition Winner 💃
            - Inter School Basketball Champion 🏀
            - Anchored in Annual Parents day

 # projects
  projects:
    enable: true
    title: "Certifications"
    items:
      - title: Project Management Certificate
        content: Aba exam diyesi pass bhayesi aaihalxa.
        image: /images/certificates/certificate.jpg
        featured:
          name: Certificate
          link: /images/certificates/certificate.jpg
        badges:
          - "Red Hat Enterprise Linux"
          - "Linux"
          - "System Administration"
        links:
          - icon: fab fa-linkedin
            url: 
          - icon: fa fa-certificate
            url: https://www.credly.com/badges/
          - icon: fab fa-linux
            url: https://www.suhesh.com.np/


 # Learning
  learning:
    enable: true
    #title: "Self Learning"
    index: true
    items:
      - title: "Project management"
        school:
          name: "Red Hat Certified Administrator"
          url: "https://"
        date: "2081 - ongoing"
        content: |-
          Here write a paragraph maybe
          - Maybe
          - Some **focused**
          - Points
        featuredLink:
          enable: true
          name: "My Notes"
          url: "/notes/project-management.pdf"

  # Achievements
  achievements:
    enable: true
    #title: ""
    items:
      - title: Something
        content: I learnt this and this and that
        url: https://www.credly.com/badges/
        image: /images/badges/asas.jpg
      - title: Next thing
        content: I learnt this and this and that
        url: https://www.credly.com/badges/
        image: /images/badges/sasa.jpg
      - title: Next nect thing
        content: I learnt this and this and that
        url: https://www.credly.com/badges/
        image: /images/badges/dada.jpg

   #Contact
  contact:
    enable: true
    # title: "Custom Name"
    content: My inbox is always open. Whether you have a question or just want to say hi, I'll try my best to get back to you!
    email: <EMAIL>
    btnName: Say Hello!
    formspree:
      enable: true # `contact.email` value will be ignored
      formId:  # Take it from your form's endpoint, like 'https://formspree.io/f/abcdefgh'
      emailCaption: "Please provide your email address"
      messageCaption: "Enter your message here"

  footer:
    recentPosts:
      path: "blogs"
      count: 3
      title: Recent Posts
      enable: true
      disableFeaturedImage: false
    socialNetworks:
      github: https://github.com/
      linkedin: https://www.linkedin.com/in/
      twitter: https://twitter.com/
      instagram: https://www.instagram.com/
      facebook: https://www.facebook.com/

  # List pages like blogs and posts
  listPages:
    disableFeaturedImage: false

  # Single pages like blog and post
  singlePages:
    readTime:
      enable: true
      content: "min read"
    scrollprogress:
      enable: true

  # For translations
  terms:
    read: "Read"
    toc: "Table Of Contents"
    copyright: "Shreya Ghimire"
    pageNotFound: "Page not found"
    emailText: "Check out this site"

  datesFormat:
    article: "Jan 2, 2006"
    articleList: "Jan 2, 2006"
    articleRecent: "Jan 2, 2006"

  #customScripts: -| # You can add custom scripts which will be added before </body> tag
  #  <script type="text/javascript"><!-- any script here --></script>
