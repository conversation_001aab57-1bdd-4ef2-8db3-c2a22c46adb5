/* Neo-brutalism Today I Learned (TIL) Section with Cute Elements */

/* TIL section container */
.til-section {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .til-section {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.til-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(#A2D2FF 6px, transparent 6px),
        radial-gradient(#A2D2FF 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .til-section::before {
    background-image: 
        radial-gradient(rgba(162, 210, 255, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(162, 210, 255, 0.3) 6px, transparent 6px);
}

/* Section title */
.til-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #A2D2FF;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .til-title {
    color: #fff;
    background-color: rgba(162, 210, 255, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* TIL description */
.til-description {
    position: relative;
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.1rem;
    line-height: 1.6;
    z-index: 1;
}

.dark .til-description {
    color: rgba(255, 255, 255, 0.9);
}

/* TIL cards container */
.til-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
    position: relative;
    z-index: 1;
}

/* TIL card */
.til-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.dark .til-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.til-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .til-card:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* TIL card header */
.til-card-header {
    padding: 15px;
    background-color: #A2D2FF;
    border-bottom: 3px solid #000;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark .til-card-header {
    background-color: rgba(162, 210, 255, 0.3);
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* TIL card date */
.til-card-date {
    font-size: 0.9rem;
    font-weight: 600;
    color: #000;
    display: flex;
    align-items: center;
    gap: 5px;
}

.dark .til-card-date {
    color: #fff;
}

/* TIL card category */
.til-card-category {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 3px 8px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 2px 2px 0 rgba(0, 0, 0, 0.7);
}

.dark .til-card-category {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 2px 2px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* TIL card content */
.til-card-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* TIL card title */
.til-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .til-card-title {
    color: #fff;
}

/* TIL card description */
.til-card-description {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 15px;
    flex-grow: 1;
}

.dark .til-card-description {
    color: rgba(255, 255, 255, 0.7);
}

/* TIL card footer */
.til-card-footer {
    padding: 15px;
    border-top: 1px dashed #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark .til-card-footer {
    border-top: 1px dashed rgba(255, 255, 255, 0.3);
}

/* TIL card tags */
.til-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.til-card-tag {
    background-color: #f0f0f0;
    color: #555;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 2px 8px;
    font-size: 0.75rem;
}

.dark .til-card-tag {
    background-color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.7);
}

/* TIL card read more */
.til-card-read {
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
}

.dark .til-card-read {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-card-read:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .til-card-read:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Cute decorative elements */
.til-section::after {
    content: "✦";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #A2D2FF;
    z-index: 0;
}

.dark .til-section::after {
    color: rgba(162, 210, 255, 0.7);
}

/* Empty state */
.til-empty-state {
    text-align: center;
    padding: 40px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    margin: 20px 0;
    position: relative;
    z-index: 1;
}

.dark .til-empty-state {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.til-empty-state-icon {
    font-size: 3rem;
    color: #A2D2FF;
    margin-bottom: 20px;
}

.dark .til-empty-state-icon {
    color: rgba(162, 210, 255, 0.5);
}

.til-empty-state-text {
    font-size: 1.2rem;
    color: #555;
    margin-bottom: 20px;
}

.dark .til-empty-state-text {
    color: rgba(255, 255, 255, 0.7);
}

/* Pagination */
.til-pagination {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.til-pagination .pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.til-pagination .page-item {
    display: inline-block;
}

.til-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .til-pagination .page-link {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-pagination .page-link:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .til-pagination .page-link:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-pagination .page-item.active .page-link {
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
}

.dark .til-pagination .page-item.active .page-link {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.til-pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Single TIL page */
.til-single {
    position: relative;
    padding: 40px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    z-index: 1;
}

.dark .til-single {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Single TIL header */
.til-single-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px dashed #ccc;
}

.dark .til-single-header {
    border-bottom: 2px dashed rgba(255, 255, 255, 0.3);
}

/* Single TIL title */
.til-single-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.dark .til-single-title {
    color: #fff;
}

/* Single TIL meta */
.til-single-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

/* Single TIL date */
.til-single-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 1rem;
    color: #555;
}

.dark .til-single-date {
    color: rgba(255, 255, 255, 0.7);
}

/* Single TIL category */
.til-single-category {
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 12px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.dark .til-single-category {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Single TIL content */
.til-single-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
}

.dark .til-single-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Single TIL content styling */
.til-single-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #000;
    margin: 30px 0 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #A2D2FF;
}

.dark .til-single-content h2 {
    color: #fff;
    border-bottom: 2px solid rgba(162, 210, 255, 0.3);
}

.til-single-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin: 25px 0 15px;
}

.dark .til-single-content h3 {
    color: #fff;
}

.til-single-content p {
    margin-bottom: 20px;
}

.til-single-content ul, .til-single-content ol {
    margin-bottom: 20px;
    padding-left: 20px;
}

.til-single-content li {
    margin-bottom: 10px;
}

.til-single-content code {
    background-color: #f0f0f0;
    color: #333;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}

.dark .til-single-content code {
    background-color: #333;
    color: rgba(255, 255, 255, 0.9);
}

.til-single-content pre {
    background-color: #f0f0f0;
    color: #333;
    padding: 15px;
    border-radius: 0;
    border: 2px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    overflow-x: auto;
    margin-bottom: 20px;
}

.dark .til-single-content pre {
    background-color: #333;
    color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.til-single-content blockquote {
    border-left: 5px solid #A2D2FF;
    padding: 15px;
    margin: 20px 0;
    background-color: #f9f9f9;
    font-style: italic;
}

.dark .til-single-content blockquote {
    border-left: 5px solid rgba(162, 210, 255, 0.3);
    background-color: #333;
}

/* Single TIL tags */
.til-single-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 30px;
}

.til-single-tag {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.dark .til-single-tag {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-single-tag:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .til-single-tag:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Single TIL navigation */
.til-single-nav {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 2px dashed #ccc;
}

.dark .til-single-nav {
    border-top: 2px dashed rgba(255, 255, 255, 0.3);
}

.til-single-nav-link {
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.dark .til-single-nav-link {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-single-nav-link:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .til-single-nav-link:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.til-single-nav-link.prev {
    margin-right: auto;
}

.til-single-nav-link.next {
    margin-left: auto;
}

.til-single-nav-link.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Responsive design */
@media (max-width: 991px) {
    .til-section {
        margin: 30px 15px;
        padding: 40px 0;
    }
    
    .til-cards {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
    }
    
    .til-single {
        padding: 30px;
        margin: 30px 15px;
    }
    
    .til-single-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .til-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }
    
    .til-description {
        font-size: 1rem;
    }
    
    .til-card-title {
        font-size: 1.1rem;
    }
    
    .til-card-content {
        padding: 15px;
    }
    
    .til-single-content {
        font-size: 1rem;
    }
    
    .til-single-content h2 {
        font-size: 1.5rem;
    }
    
    .til-single-content h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .til-section {
        margin: 20px 10px;
        padding: 30px 0;
    }
    
    .til-cards {
        grid-template-columns: 1fr;
    }
    
    .til-single {
        padding: 20px;
        margin: 20px 10px;
    }
    
    .til-single-title {
        font-size: 1.8rem;
    }
    
    .til-single-meta {
        flex-direction: column;
        gap: 10px;
    }
    
    .til-single-nav {
        flex-direction: column;
        gap: 15px;
    }
    
    .til-single-nav-link.prev,
    .til-single-nav-link.next {
        margin: 0;
    }
}
