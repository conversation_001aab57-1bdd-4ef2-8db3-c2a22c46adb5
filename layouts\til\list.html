{{ define "head" }}
<meta name="description" content="Today I Learned - {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/til-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
Today I Learned | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="til-section">
    <div class="container">
        <div class="text-center">
            <h1 class="til-title" data-aos="fade-up" data-aos-duration="800">Today I Learned</h1>
            <p class="til-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                A collection of things I've learned day to day. Small snippets of knowledge that I've picked up along the way.
            </p>
        </div>

        {{ if len .Pages }}
        <div class="til-cards">
            {{ range .Paginator.Pages }}
            <div class="til-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="til-card-header">
                    <div class="til-card-date">
                        <i class="far fa-calendar-alt"></i>
                        {{ .Date.Format "Jan 2, 2006" }}
                    </div>
                    {{ if .Params.category }}
                    <span class="til-card-category">{{ .Params.category }}</span>
                    {{ end }}
                </div>
                <div class="til-card-content">
                    <h2 class="til-card-title">{{ .Title }}</h2>
                    <div class="til-card-description">
                        {{ if .Params.description }}
                        {{ .Params.description | truncate 120 }}
                        {{ else }}
                        {{ .Summary | truncate 120 }}
                        {{ end }}
                    </div>
                    <div class="til-card-footer">
                        <div class="til-card-tags">
                            {{ range .Params.tags }}
                            <span class="til-card-tag">{{ . }}</span>
                            {{ end }}
                        </div>
                        <a href="{{ .Permalink }}" class="til-card-read">Read More</a>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <!-- Pagination -->
        {{ if gt .Paginator.TotalPages 1 }}
        <div class="til-pagination" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
            <ul class="pagination">
                {{ if .Paginator.HasPrev }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Prev.URL }}" aria-label="Previous">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                </li>
                {{ end }}

                {{ $paginator := .Paginator }}
                {{ $adjacent_links := 2 }}
                {{ $max_links := (add (mul $adjacent_links 2) 1) }}
                {{ $lower_limit := (add $adjacent_links 1) }}
                {{ $upper_limit := (sub $paginator.TotalPages $adjacent_links) }}

                {{ if gt $paginator.TotalPages $max_links }}
                    {{ if le $paginator.PageNumber $lower_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if le .PageNumber $max_links }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else if ge $paginator.PageNumber $upper_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if gt .PageNumber (sub $paginator.TotalPages $max_links) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else }}
                        {{ range $paginator.Pagers }}
                            {{ if and (ge .PageNumber (sub $paginator.PageNumber $adjacent_links)) (le .PageNumber (add $paginator.PageNumber $adjacent_links)) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ end }}
                {{ else }}
                    {{ range $paginator.Pagers }}
                        <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                            <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                        </li>
                    {{ end }}
                {{ end }}

                {{ if .Paginator.HasNext }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Next.URL }}" aria-label="Next">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                </li>
                {{ end }}
            </ul>
        </div>
        {{ end }}
        {{ else }}
        <div class="til-empty-state" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <div class="til-empty-state-icon">
                <i class="fas fa-lightbulb"></i>
            </div>
            <div class="til-empty-state-text">No TIL entries yet. Check back soon!</div>
        </div>
        {{ end }}
    </div>
</section>
{{ end }}
