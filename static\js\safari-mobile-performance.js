/**
 * Safari and Mobile Performance Optimizations
 * Fixes compatibility issues and improves performance on Safari and mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    // Detect Safari browser
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    
    // Add browser-specific classes
    if (isSafari) {
        document.body.classList.add('safari');
    }
    if (isMobile) {
        document.body.classList.add('mobile');
    }
    if (isIOS) {
        document.body.classList.add('ios');
    }
    
    // Safari-specific fixes
    if (isSafari) {
        // Fix for Safari transform issues
        const elementsWithTransform = document.querySelectorAll('.gallery-item, .character-container, .error-page');
        elementsWithTransform.forEach(element => {
            element.style.webkitTransformStyle = 'preserve-3d';
            element.style.webkitBackfaceVisibility = 'hidden';
        });
        
        // Fix for Safari animation performance
        const animatedElements = document.querySelectorAll('.gallery-item-inner, .gallery-image');
        animatedElements.forEach(element => {
            element.style.webkitTransform = 'translateZ(0)';
            element.style.webkitWillChange = 'transform';
        });
    }
    
    // Mobile performance optimizations
    if (isMobile) {
        // Reduce animation complexity on mobile
        const complexAnimations = document.querySelectorAll('.error-page');
        complexAnimations.forEach(element => {
            element.style.animation = 'none';
        });
        
        // Optimize touch interactions
        const touchElements = document.querySelectorAll('.gallery-item, .home-button, .filter-button');
        touchElements.forEach(element => {
            element.style.touchAction = 'manipulation';
            element.style.webkitTapHighlightColor = 'transparent';
        });
        
        // Throttle scroll events for better performance
        let scrollTimeout;
        const originalScrollHandler = window.onscroll;
        window.onscroll = function() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(function() {
                if (originalScrollHandler) {
                    originalScrollHandler();
                }
            }, 16); // ~60fps
        };
    }
    
    // iOS Safari specific fixes
    if (isIOS) {
        // Fix for iOS Safari viewport issues
        const fixViewport = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };
        
        fixViewport();
        window.addEventListener('resize', fixViewport);
        window.addEventListener('orientationchange', () => {
            setTimeout(fixViewport, 100);
        });
        
        // Fix for iOS Safari scroll bounce
        document.body.style.webkitOverflowScrolling = 'touch';
        
        // Prevent zoom on input focus
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.style.fontSize === '' || parseFloat(input.style.fontSize) < 16) {
                input.style.fontSize = '16px';
            }
        });
    }
    
    // Gallery performance optimizations
    const galleryItems = document.querySelectorAll('.gallery-item');
    if (galleryItems.length > 0) {
        // Use Intersection Observer for lazy loading animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '50px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);
        
        galleryItems.forEach(item => {
            observer.observe(item);
        });
        
        // Optimize gallery hover effects for mobile
        if (isMobile) {
            galleryItems.forEach(item => {
                const inner = item.querySelector('.gallery-item-inner');
                const image = item.querySelector('.gallery-image');
                
                if (inner && image) {
                    // Replace hover with touch events
                    item.addEventListener('touchstart', () => {
                        inner.style.transform = 'translate3d(-3px, -3px, 0)';
                        image.style.transform = 'scale3d(1.02, 1.02, 1)';
                    });
                    
                    item.addEventListener('touchend', () => {
                        inner.style.transform = '';
                        image.style.transform = '';
                    });
                }
            });
        }
    }
    
    // 404 page optimizations
    const errorPage = document.querySelector('.error-page');
    if (errorPage) {
        // Optimize character animations for mobile
        const character = document.querySelector('.character-container');
        if (character && isMobile) {
            character.addEventListener('touchstart', () => {
                character.style.transform = 'scale(1.02) translateZ(0)';
            });
            
            character.addEventListener('touchend', () => {
                character.style.transform = 'scale(1) translateZ(0)';
            });
        }
        
        // Optimize confetti animation
        const confettiContainer = document.getElementById('confettiContainer');
        if (confettiContainer) {
            const originalCreateConfetti = window.createConfetti;
            if (originalCreateConfetti && isMobile) {
                window.createConfetti = function() {
                    // Reduce confetti count on mobile
                    const confettiCount = 15; // Reduced from default
                    for (let i = 0; i < confettiCount; i++) {
                        const confetti = document.createElement('div');
                        confetti.className = 'confetti';
                        confetti.style.left = Math.random() * 100 + '%';
                        confetti.style.animationDelay = Math.random() * 2 + 's';
                        confetti.style.animationDuration = '2s'; // Faster animation
                        confettiContainer.appendChild(confetti);
                        
                        // Remove confetti after animation
                        setTimeout(() => {
                            if (confetti.parentNode) {
                                confetti.parentNode.removeChild(confetti);
                            }
                        }, 2000);
                    }
                };
            }
        }
    }
    
    // Lightbox optimizations
    const lightbox = document.querySelector('.lightbox');
    if (lightbox) {
        // Add touch support for lightbox navigation
        let touchStartX = 0;
        let touchEndX = 0;
        
        lightbox.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        });
        
        lightbox.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
        
        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartX - touchEndX;
            
            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    // Swipe left - next image
                    const nextButton = document.querySelector('.lightbox-next');
                    if (nextButton) nextButton.click();
                } else {
                    // Swipe right - previous image
                    const prevButton = document.querySelector('.lightbox-prev');
                    if (prevButton) prevButton.click();
                }
            }
        }
    }
    
    // Performance monitoring and optimization
    if (isMobile || isSafari) {
        // Debounce resize events
        let resizeTimeout;
        window.addEventListener('resize', () => {
            if (resizeTimeout) {
                clearTimeout(resizeTimeout);
            }
            resizeTimeout = setTimeout(() => {
                // Trigger any necessary recalculations
                const event = new Event('optimizedResize');
                window.dispatchEvent(event);
            }, 250);
        });
        
        // Optimize scroll performance
        let ticking = false;
        function updateScrollElements() {
            // Update any scroll-dependent elements here
            ticking = false;
        }
        
        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollElements);
                ticking = true;
            }
        });
    }
    
    // Add CSS class for JavaScript-enabled optimizations
    document.documentElement.classList.add('js-optimized');
    
    console.log('Safari and mobile optimizations loaded', {
        isSafari,
        isMobile,
        isIOS,
        userAgent: navigator.userAgent
    });
});

// Polyfill for older browsers
if (!window.requestAnimationFrame) {
    window.requestAnimationFrame = function(callback) {
        return setTimeout(callback, 1000 / 60);
    };
}

if (!window.cancelAnimationFrame) {
    window.cancelAnimationFrame = function(id) {
        clearTimeout(id);
    };
}
