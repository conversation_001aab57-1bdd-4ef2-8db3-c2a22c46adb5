---
title: "Creating Custom Hugo Shortcodes"
date: 2023-07-15T10:30:00+05:30
description: "How to create and use custom shortcodes in Hugo to simplify content creation"
category: "Web Development"
tags: ["hugo", "static-site", "shortcodes", "templates"]
---

Today I learned how to create custom shortcodes in <PERSON>. Shortcodes are a simple snippet inside your content files that <PERSON> will render using a predefined template.

## Basic Shortcode

To create a basic shortcode, create a file in `layouts/shortcodes/` directory. For example, to create a `note` shortcode:

```html
<!-- layouts/shortcodes/note.html -->
<div class="note">
  {{ .Inner }}
</div>
```

Then use it in your markdown like this:

```markdown
{{%/* note */%}}
This is a note!
{{%/* /note */%}}
```

## Shortcodes with Parameters

You can also create shortcodes that accept parameters:

```html
<!-- layouts/shortcodes/box.html -->
<div class="box {{ .Get "class" }}">
  {{ .Inner }}
</div>
```

And use it like:

```markdown
{{%/* box class="info" */%}}
This is an info box!
{{%/* /box */%}}
```

## Self-closing Shortcodes

Not all shortcodes need inner content. For example, a YouTube embed:

```html
<!-- layouts/shortcodes/youtube.html -->
<div class="youtube">
  <iframe src="https://www.youtube.com/embed/{{ .Get 0 }}" allowfullscreen></iframe>
</div>
```

Use it like:

```markdown
{{</* youtube dQw4w9WgXcQ */>}}
```

This has been super helpful for creating consistent UI elements across my site!
