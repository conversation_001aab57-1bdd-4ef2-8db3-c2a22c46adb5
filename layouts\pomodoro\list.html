{{ define "head" }}
<meta name="description" content="{{ .Description }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/pomodoro-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="pomodoro-section">
    <div class="container">
        <h1 class="pomodoro-title" data-aos="fade-up" data-aos-duration="800">{{ .Title }}</h1>
        <div class="pomodoro-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            {{ .Content }}
        </div>

        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="pomodoro-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                    <div class="pomodoro-card-header">
                        <div class="pomodoro-mode-selector">
                            <button class="pomodoro-mode-btn active" data-mode="work">Work</button>
                            <button class="pomodoro-mode-btn" data-mode="break">Break</button>
                        </div>
                    </div>
                    
                    <div class="pomodoro-card-body">
                        <div class="pomodoro-timer">
                            <div class="pomodoro-timer-display">
                                <span id="minutes">25</span>:<span id="seconds">00</span>
                            </div>
                            <div class="pomodoro-timer-controls">
                                <button id="start-btn" class="pomodoro-btn primary">Start</button>
                                <button id="pause-btn" class="pomodoro-btn secondary" disabled>Pause</button>
                                <button id="reset-btn" class="pomodoro-btn danger">Reset</button>
                            </div>
                        </div>
                        
                        <div class="pomodoro-settings">
                            <div class="pomodoro-settings-group">
                                <label for="work-duration">Work Duration (minutes)</label>
                                <input type="range" id="work-duration" min="1" max="60" value="25" class="pomodoro-range">
                                <span id="work-duration-value">25</span>
                            </div>
                            <div class="pomodoro-settings-group">
                                <label for="break-duration">Break Duration (minutes)</label>
                                <input type="range" id="break-duration" min="1" max="30" value="5" class="pomodoro-range">
                                <span id="break-duration-value">5</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="ambient-sounds-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                    <h2 class="ambient-sounds-title">Ambient Sounds</h2>
                    <div class="ambient-sounds-container">
                        {{ range .Site.Params.pomodoro.ambientSounds }}
                        <div class="ambient-sound-item">
                            <div class="ambient-sound-info">
                                <span class="ambient-sound-name">{{ .name }}</span>
                                <div class="ambient-sound-controls">
                                    <button class="ambient-sound-btn play" data-sound="{{ .file }}">
                                        <i class="fas fa-play"></i>
                                    </button>
                                    <input type="range" class="ambient-sound-volume" min="0" max="100" value="50">
                                </div>
                            </div>
                        </div>
                        {{ end }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="{{ .Site.Params.staticPath }}/js/pomodoro.js"></script>
{{ end }}
