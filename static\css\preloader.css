/* Preloader styles */
.preloader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fffef0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.dark .preloader-wrapper {
    background-color: #1a1a1a;
}

.preloader-wrapper.fade-out {
    opacity: 0;
    visibility: hidden;
}

/* Loader container with neo-brutalism styling */
.loader-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
}

.dark .loader-container {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Loader text */
.loader-text {
    margin-top: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .loader-text {
    color: #FFD1DC;
}

/* Loader animation */
.loader {
    width: 35px;
    height: 80px;
    position: relative;
}

/* Light mode loader */
.loader:after {
    content: "";
    position: absolute;
    inset: 0;
    padding: 3px 5px;
    border-top: 1px solid #bbb6aa;
    border-bottom: 4px solid #bbb6aa;
    background: linear-gradient(#612329 0 0) bottom no-repeat content-box, #e4e0d7;
    mix-blend-mode: darken;
    animation: l1 1.5s infinite linear;
}

.loader:before {
    content: "";
    position: absolute;
    inset: -18px calc(50% - 2px) 8px;
    background: #eb6b3e;
    transform-origin: bottom;
    transform: rotate(8deg);
}

/* Dark mode loader - completely redesigned for visibility */
.dark .loader {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top-color: #FFD1DC;
    animation: darkLoader 1s infinite linear;
    box-shadow: 0 0 20px rgba(255, 209, 220, 0.5);
}

.dark .loader:before {
    content: none;
}

.dark .loader:after {
    content: none;
}

@keyframes darkLoader {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes l1 {
    0% {
        background-size: 100% 100%;
    }
    100% {
        background-size: 100% 5%;
    }
}
