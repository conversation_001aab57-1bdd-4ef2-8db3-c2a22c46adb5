/* Neo-brutalism Blog Styles with Cute Elements */

/* Blog list container */
.blog-list-container {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .blog-list-container {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.blog-list-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .blog-list-container::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Blog list title */
.blog-list-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 2.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #FFD1DC;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .blog-list-title {
    color: #fff;
    background-color: rgba(255, 209, 220, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Blog card */
.blog-card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
}

.dark .blog-card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.blog-card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark .blog-card:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Blog card image container */
.blog-card-image {
    position: relative;
    overflow: hidden;
    height: 200px;
    background-color: #f0f0f0;
    z-index: 1;
    border-bottom: 2px solid #000;
}

.dark .blog-card-image {
    background-color: #333;
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

/* Blog card image */
.blog-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    z-index: 2;
    display: block;
}

.blog-card:hover .blog-card-image img {
    transform: scale(1.05);
}

/* Blog card content */
.blog-card-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

/* Blog card title */
.blog-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #000;
    font-family: 'Playfair Display', serif;
}

.dark .blog-card-title {
    color: #fff;
}

/* Blog card description */
.blog-card-description {
    font-size: 0.95rem;
    color: #555;
    margin-bottom: 15px;
    flex-grow: 1;
}

.dark .blog-card-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Blog card footer */
.blog-card-footer {
    padding: 15px;
    border-top: 1px dashed #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark .blog-card-footer {
    border-top: 1px dashed rgba(255, 255, 255, 0.3);
}

/* Blog card date */
.blog-card-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #777;
}

.dark .blog-card-date {
    color: rgba(255, 255, 255, 0.6);
}

/* Blog card read more */
.blog-card-read {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
}

.dark .blog-card-read {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.blog-card-read:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .blog-card-read:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Blog card tags */
.blog-card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.blog-card-tag {
    background-color: #f0f0f0;
    color: #555;
    border: 1px solid #ccc;
    border-radius: 20px;
    padding: 2px 8px;
    font-size: 0.75rem;
}

.dark .blog-card-tag {
    background-color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.7);
}

/* Single blog post */
.blog-single {
    position: relative;
    padding: 40px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    z-index: 1;
}

.dark .blog-single {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Single blog header */
.blog-single-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px dashed #ccc;
}

.dark .blog-single-header {
    border-bottom: 2px dashed rgba(255, 255, 255, 0.3);
}

/* Single blog title */
.blog-single-title {
    font-size: 2.5rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.dark .blog-single-title {
    color: #fff;
}

/* Single blog meta */
.blog-single-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

/* Single blog date */
.blog-single-date {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 1rem;
    color: #555;
}

.dark .blog-single-date {
    color: rgba(255, 255, 255, 0.7);
}

/* Single blog content */
.blog-single-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    margin-bottom: 30px;
}

.dark .blog-single-content {
    color: rgba(255, 255, 255, 0.9);
}

/* Single blog content styling */
.blog-single-content h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #000;
    margin: 30px 0 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #FFD1DC;
}

.dark .blog-single-content h2 {
    color: #fff;
    border-bottom: 2px solid rgba(255, 209, 220, 0.3);
}

.blog-single-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin: 25px 0 15px;
}

.dark .blog-single-content h3 {
    color: #fff;
}

.blog-single-content p {
    margin-bottom: 20px;
}

.blog-single-content ul, .blog-single-content ol {
    margin-bottom: 20px;
    padding-left: 20px;
}

.blog-single-content li {
    margin-bottom: 10px;
}

.blog-single-content code {
    background-color: #f0f0f0;
    color: #333;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}

.dark .blog-single-content code {
    background-color: #333;
    color: rgba(255, 255, 255, 0.9);
}

.blog-single-content pre {
    background-color: #f0f0f0;
    color: #333;
    padding: 15px;
    border-radius: 0;
    border: 2px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    overflow-x: auto;
    margin-bottom: 20px;
}

.dark .blog-single-content pre {
    background-color: #333;
    color: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.blog-single-content blockquote {
    border-left: 5px solid #FFD1DC;
    padding: 15px;
    margin: 20px 0;
    background-color: #f9f9f9;
    font-style: italic;
}

.dark .blog-single-content blockquote {
    border-left: 5px solid rgba(255, 209, 220, 0.3);
    background-color: #333;
}

/* Single blog tags */
.blog-single-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 30px;
}

.blog-single-tag {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.dark .blog-single-tag {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.blog-single-tag:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .blog-single-tag:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Cute decorative elements */
.blog-list-container::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark .blog-list-container::after {
    color: rgba(255, 107, 139, 0.7);
}

.blog-single::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .blog-single::before {
    color: rgba(255, 107, 139, 0.7);
}

.blog-single::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .blog-single::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Pagination */
.blog-pagination {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.blog-pagination .pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 10px;
}

.blog-pagination .page-item {
    display: inline-block;
}

.blog-pagination .page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: #fff;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .blog-pagination .page-link {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.blog-pagination .page-link:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark .blog-pagination .page-link:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.blog-pagination .page-item.active .page-link {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
}

.dark .blog-pagination .page-item.active .page-link {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.blog-pagination .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 991px) {
    .blog-list-container {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .blog-single {
        padding: 30px;
        margin: 30px 15px;
    }

    .blog-single-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .blog-list-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .blog-card-title {
        font-size: 1.1rem;
    }

    .blog-card-content {
        padding: 15px;
    }

    .blog-single-content {
        font-size: 1rem;
    }

    .blog-single-content h2 {
        font-size: 1.5rem;
    }

    .blog-single-content h3 {
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .blog-list-container {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .blog-single {
        padding: 20px;
        margin: 20px 10px;
    }

    .blog-single-title {
        font-size: 1.8rem;
    }

    .blog-single-meta {
        flex-direction: column;
        gap: 10px;
    }
}

/* Override for existing blog styles */
#blog .post {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    margin-bottom: 30px;
}

.dark #blog .post {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

#blog .post:hover {
    transform: translate(-5px, -5px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark #blog .post:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

#blog .post-footer {
    padding: 15px;
    border-top: 1px dashed #ccc;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dark #blog .post-footer {
    border-top: 1px dashed rgba(255, 255, 255, 0.3);
}

#blog .badge-neo {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: #777;
    background: none;
    border: none;
    padding: 0;
}

.dark #blog .badge-neo {
    color: rgba(255, 255, 255, 0.6);
}

#blog .btn-neo {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    text-decoration: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    display: inline-block;
}

.dark #blog .btn-neo {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

#blog .btn-neo:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    text-decoration: none;
    color: #000;
}

.dark #blog .btn-neo:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Single blog page */
#single {
    position: relative;
    padding: 40px 0;
}

#single .title h1 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 15px;
    font-family: 'Playfair Display', serif;
}

.dark #single .title h1 {
    color: #fff;
}

#single .page-content {
    position: relative;
    padding: 40px;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 0;
    z-index: 1;
}

.dark #single .page-content {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

#single .page-content::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark #single .page-content::before {
    color: rgba(255, 107, 139, 0.7);
}

#single .page-content::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark #single .page-content::after {
    color: rgba(255, 107, 139, 0.7);
}
