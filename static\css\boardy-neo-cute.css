/* Neo-brutalism Whiteboard (Boardy) Styles */

/* Boardy container */
.boardy-container {
    position: relative;
    padding: 40px 20px;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .boardy-container {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.boardy-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#C7CEEA 6px, transparent 6px),
        radial-gradient(#C7CEEA 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .boardy-container::before {
    background-image:
        radial-gradient(rgba(199, 206, 234, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(199, 206, 234, 0.3) 6px, transparent 6px);
}

/* Boardy title */
.boardy-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #C7CEEA;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .boardy-title {
    color: #fff;
    background-color: rgba(199, 206, 234, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Boardy description */
.boardy-description {
    position: relative;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
}

.dark .boardy-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Canvas container */
.canvas-container {
    position: relative;
    margin: 0 auto;
    z-index: 1;
    max-width: 1000px;
}

/* Canvas */
#boardy-canvas {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    cursor: crosshair;
    touch-action: none;
    width: 100%;
    height: 600px;
}

.dark #boardy-canvas {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

@media (max-width: 768px) {
    #boardy-canvas {
        height: 450px;
    }
}

@media (max-width: 576px) {
    #boardy-canvas {
        height: 350px;
    }
}

/* Tools container */
.tools-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    position: relative;
    z-index: 1;
}

/* Tool button */
.tool-button {
    background-color: #fff;
    color: #000;
    border: 3px solid #000;
    border-radius: 0;
    padding: 10px 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    gap: 5px;
}

.dark .tool-button {
    background-color: #2a2a2a;
    color: #fff;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.tool-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .tool-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.tool-button.active {
    background-color: #FFD1DC;
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .tool-button.active {
    background-color: rgba(255, 209, 220, 0.3);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Color picker */
.color-picker {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 20px;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.color-option {
    width: 30px;
    height: 30px;
    border: 3px solid #000;
    border-radius: 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dark .color-option {
    border: 3px solid rgba(255, 255, 255, 0.7);
}

.color-option:hover {
    transform: scale(1.1);
}

.color-option.active {
    transform: scale(1.1);
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .color-option.active {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

/* Emoji and shape pickers */
.emoji-picker, .shape-picker {
    display: none;
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 10px;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    z-index: 10;
    max-width: 400px;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
    justify-content: center;
}

.dark .emoji-picker, .dark .shape-picker {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.emoji-option, .shape-option {
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    transition: all 0.3s ease;
    min-width: 30px;
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.emoji-option:hover, .shape-option:hover {
    transform: scale(1.2);
    background-color: rgba(0, 0, 0, 0.1);
}

.dark .emoji-option:hover, .dark .shape-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Text input container */
.text-input-container {
    display: none;
    margin: 10px auto;
    max-width: 500px;
    position: relative;
    z-index: 5;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 15px;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .text-input-container {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.text-input {
    padding: 10px;
    border: 3px solid #000;
    border-radius: 0;
    font-size: 1rem;
    width: 70%;
    background-color: #fff;
}

.dark .text-input {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

#add-text-button {
    padding: 10px 15px;
    width: auto;
    background-color: #A2D2FF;
    color: #000;
    font-weight: 600;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dark #add-text-button {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

#add-text-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark #add-text-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Size slider */
.size-slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.size-slider-label {
    font-weight: 600;
    color: #000;
}

.dark .size-slider-label {
    color: #fff;
}

.size-slider {
    -webkit-appearance: none;
    width: 200px;
    height: 10px;
    background: #fff;
    border: 2px solid #000;
    border-radius: 0;
    outline: none;
}

.dark .size-slider {
    background: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.size-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    cursor: pointer;
}

.dark .size-slider::-webkit-slider-thumb {
    background: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.size-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    cursor: pointer;
}

.dark .size-slider::-moz-range-thumb {
    background: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Clear notice */
.clear-notice {
    margin-top: 20px;
    padding: 10px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    text-align: center;
    font-size: 0.9rem;
    color: #000;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
}

.dark .clear-notice {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

/* Responsive design */
@media (max-width: 991px) {
    .boardy-container {
        margin: 30px 15px;
        padding: 30px 15px;
        box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .boardy-container {
        box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    }

    .boardy-title {
        font-size: 1.8rem;
    }

    .tools-container {
        gap: 8px;
    }

    .tool-button {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    .boardy-container {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .boardy-container {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }

    .boardy-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }

    .boardy-description {
        font-size: 1rem;
    }

    .tools-container {
        gap: 5px;
    }

    .tool-button {
        padding: 6px 10px;
        font-size: 0.8rem;
    }

    .color-option {
        width: 25px;
        height: 25px;
    }

    .size-slider {
        width: 150px;
    }
}

@media (max-width: 576px) {
    .boardy-container {
        margin: 15px 5px;
        padding: 15px 5px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }

    .dark .boardy-container {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }

    .boardy-title {
        font-size: 1.3rem;
        padding: 6px 12px;
    }

    .tools-container {
        flex-direction: column;
        align-items: center;
    }

    .tool-button {
        width: 100%;
        max-width: 200px;
        justify-content: center;
    }
}
