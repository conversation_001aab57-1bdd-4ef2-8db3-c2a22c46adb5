/* Neo-brutalism Table Styles with Cute Elements */

/* Table container */
table {
    width: 100%;
    margin: 30px 0;
    border-collapse: separate;
    border-spacing: 0;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    overflow: hidden;
    position: relative;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.dark table {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

table:hover {
    transform: translate(-3px, -3px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark table:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}

/* Table header */
thead tr th {
    padding: 15px 20px !important;
    background-color: #FFD1DC !important;
    color: #000 !important;
    text-align: left;
    border: none;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700 !important;
    border-bottom: 3px solid #000 !important;
}

.dark thead tr th {
    background-color: rgba(255, 209, 220, 0.3) !important;
    color: #fff !important;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7) !important;
}

/* Table cells */
tbody tr td {
    padding: 15px 20px !important;
    border-top: 2px solid #000 !important;
    background-color: transparent !important;
    color: #000 !important;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.dark tbody tr td {
    border-top: 2px solid rgba(255, 255, 255, 0.7) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Table rows */
tbody tr {
    transition: all 0.2s ease;
}

tbody tr:hover {
    background-color: rgba(255, 209, 220, 0.2) !important;
}

.dark tbody tr:hover {
    background-color: rgba(255, 209, 220, 0.1) !important;
}

/* Alternating row colors */
tbody tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.03) !important;
}

.dark tbody tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.03) !important;
}

/* Responsive tables */
@media (max-width: 768px) {
    table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    thead tr th,
    tbody tr td {
        padding: 12px 15px !important;
    }
}

/* Table caption */
table caption {
    caption-side: top;
    padding: 12px 15px;
    background-color: #A2D2FF;
    color: #000 !important;
    font-size: 1.1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 3px solid #000;
    font-weight: 700;
}

.dark table caption {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff !important;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Table with border */
table.bordered tbody tr td {
    border: 2px solid #000 !important;
}

.dark table.bordered tbody tr td {
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
}

/* First column styling */
tbody tr td:first-child {
    font-weight: 600;
}

/* Last row styling */
tbody tr:last-child {
    border-bottom: none;
}

/* Cute decorative elements */
table::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 1.5rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark table::before {
    color: rgba(255, 107, 139, 0.7);
}

table::after {
    content: "♥";
    position: absolute;
    bottom: -15px;
    right: -15px;
    font-size: 1.5rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark table::after {
    color: rgba(255, 107, 139, 0.7);
}

/* Specific styles for blog posts */
#single .page-content table {
    width: 100%;
    margin: 30px 0;
    border-collapse: separate;
    border-spacing: 0;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    overflow: hidden;
    position: relative;
}

.dark #single .page-content table {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

#single .page-content table > thead > tr > th {
    padding: 15px 20px !important;
    background-color: #FFD1DC !important;
    color: #000 !important;
    text-align: left;
    border: none;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 700 !important;
    border-bottom: 3px solid #000 !important;
}

.dark #single .page-content table > thead > tr > th {
    background-color: rgba(255, 209, 220, 0.3) !important;
    color: #fff !important;
    border-bottom: 3px solid rgba(255, 255, 255, 0.7) !important;
}

#single .page-content table > tbody > tr > td {
    padding: 15px 20px !important;
    border-top: 2px solid #000 !important;
    background-color: transparent !important;
    color: #000 !important;
    font-size: 1rem;
}

.dark #single .page-content table > tbody > tr > td {
    border-top: 2px solid rgba(255, 255, 255, 0.7) !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

#single .page-content table > tbody > tr:hover {
    background-color: rgba(255, 209, 220, 0.2) !important;
}

.dark #single .page-content table > tbody > tr:hover {
    background-color: rgba(255, 209, 220, 0.1) !important;
}

#single .page-content table:hover {
    transform: translate(-3px, -3px);
    box-shadow: 11px 11px 0 rgba(0, 0, 0, 0.7);
}

.dark #single .page-content table:hover {
    box-shadow: 11px 11px 0 rgba(255, 255, 255, 0.15);
}
