/* Fix for navbar on mobile devices */
@media (max-width: 991px) {
    /* Make navbar scrollable on mobile but hide scrollbars */
    .navbar-collapse {
        max-height: 80vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    /* Hide scrollbar for Chrome, Safari and Opera */
    .navbar-collapse::-webkit-scrollbar {
        display: none;
        width: 0;
        height: 0;
    }

    /* Ensure the navbar doesn't close when interacting with elements inside it */
    .navbar-collapse.show {
        display: block !important;
    }

    /* Add some padding to ensure elements at the bottom are visible */
    .navbar-nav {
        padding-bottom: 30px; /* Extra padding to ensure visibility of last items */
    }

    /* Prevent navbar from hiding when interacting with theme toggle */
    #theme-toggle {
        position: relative;
        z-index: 1050;
    }

    /* Ensure the navbar stays visible when theme toggle is clicked */
    .navbar-fixed.navbar-visible {
        transform: translateY(0) !important;
    }

    /* Add a subtle indicator that content is scrollable */
    .navbar-collapse::after {
        content: "";
        display: block;
        height: 20px;
        background: linear-gradient(to bottom, transparent, rgba(0,0,0,0.05));
        position: sticky;
        bottom: 0;
        margin-top: -20px;
        pointer-events: none;
    }

    .dark .navbar-collapse::after {
        background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.05));
    }
}
