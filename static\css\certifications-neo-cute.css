/* Neo-brutalism Certifications Section */
#certifications {
    background-color: var(--background-color);
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
    padding: 30px 20px;
    position: relative;
    overflow: hidden;
}

.dark #certifications {
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
}

/* Decorative elements */
#certifications::before {
    content: "🏆";
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(15deg);
}

#certifications::after {
    content: "🎖️";
    position: absolute;
    bottom: 20px;
    left: 30px;
    font-size: 40px;
    z-index: 0;
    opacity: 0.5;
    transform: rotate(-15deg);
}

/* Section title */
#certifications h3 {
    margin-bottom: 2.5rem;
    position: relative;
    font-weight: 700;
    color: var(--text-color);
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 3px solid #000;
    z-index: 1;
}

.dark #certifications h3 {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
}

/* Certification cards */
#certifications .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.dark #certifications .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

#certifications .card:hover {
    transform: translate(-5px, -5px);
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
}

.dark #certifications .card:hover {
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Card image */
#certifications .card-img-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    border-bottom: 3px solid #000;
    background-color: #f8f8f8;
    height: 200px;
}

.dark #certifications .card-img-container {
    border-bottom: 3px solid rgba(255, 255, 255, 0.7);
    background-color: #222;
}

#certifications .card-img {
    max-width: 100%;
    max-height: 150px;
    transition: transform 0.3s ease;
}

#certifications .card:hover .card-img {
    transform: scale(1.05);
}

/* Card content */
#certifications .card-body {
    padding: 1.5rem;
}

#certifications .card-title {
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    position: relative;
}

#certifications .card-title::after {
    content: "";
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: #C7CEEA;
}

.dark #certifications .card-title::after {
    background-color: rgba(199, 206, 234, 0.7);
}

#certifications .card-text {
    color: var(--text-secondary-color);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Certification issuer */
#certifications .cert-issuer {
    display: inline-block;
    background-color: #FFD1DC;
    color: #000;
    padding: 5px 10px;
    border: 2px solid #000;
    font-weight: 600;
    font-size: 0.8rem;
    margin-bottom: 1rem;
}

.dark #certifications .cert-issuer {
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* Certification date */
#certifications .cert-date {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #A2D2FF;
    color: #000;
    padding: 5px 10px;
    border: 2px solid #000;
    font-weight: 600;
    font-size: 0.8rem;
    z-index: 1;
}

.dark #certifications .cert-date {
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

/* View certificate button */
#certifications .btn {
    background-color: #B5EAD7;
    color: #000;
    font-weight: 600;
    padding: 8px 15px;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark #certifications .btn {
    background-color: rgba(181, 234, 215, 0.3);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

#certifications .btn:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark #certifications .btn:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #certifications {
        margin: 30px 15px;
        padding: 25px 15px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }
    
    #certifications .card {
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications .card {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #certifications .card:hover {
        transform: translate(-3px, -3px);
        box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications .card:hover {
        box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
    }
    
    #certifications .card-body {
        padding: 1rem;
    }
    
    #certifications .card-img-container {
        height: 150px;
    }
    
    #certifications .card-img {
        max-height: 100px;
    }
}

@media (max-width: 480px) {
    #certifications {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    #certifications h3 {
        font-size: 1.5rem;
    }
    
    #certifications .card {
        box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications .card {
        box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    }
    
    #certifications .card:hover {
        transform: translate(-2px, -2px);
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark #certifications .card:hover {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
}
