{{ define "head" }}
<meta name="description" content="Posts in category {{ .Title }} on {{ .Site.Title }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/taxonomy-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="taxonomy-section">
    <div class="container">
        <a href="{{ "categories" | absURL }}" class="back-button" data-aos="fade-right" data-aos-duration="800">
            <i class="fas fa-arrow-left"></i> Back to All Categories
        </a>

        <div class="taxonomy-header" data-aos="fade-up" data-aos-duration="800">
            <h1 class="taxonomy-header-title">
                <i class="fas fa-folder taxonomy-icon"></i> {{ .Title }}
            </h1>
            <span class="taxonomy-header-count">{{ len .Pages }} {{ if eq (len .Pages) 1 }}post{{ else }}posts{{ end }}</span>
        </div>

        {{ if len .Pages }}
        <div class="post-cards">
            {{ range .Paginator.Pages }}
            {{ $source := "blog" }}
            {{ if in .File.Path "til/" }}
                {{ $source = "til" }}
            {{ else if in .File.Path "exercises/" }}
                {{ $source = "exercise" }}
            {{ else if in .File.Path "habits/" }}
                {{ $source = "habit" }}
            {{ end }}
            <div class="post-card" data-source="{{ $source }}" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                {{ if .Params.image }}
                <div class="post-card-image">
                    <img src="{{ .Params.image }}" alt="{{ .Title }}">
                </div>
                {{ end }}
                <div class="post-card-content">
                    <h2 class="post-card-title">{{ .Title }}</h2>
                    <div class="post-card-description">
                        {{ if .Params.description }}
                        {{ .Params.description | truncate 120 }}
                        {{ else }}
                        {{ .Summary | truncate 120 }}
                        {{ end }}
                    </div>
                    <div class="post-card-meta">
                        <div class="post-card-date">
                            <i class="far fa-calendar-alt"></i>
                            {{ .Date.Format "Jan 2, 2006" }}
                        </div>
                        <a href="{{ .Permalink }}" class="post-card-read">Read More</a>
                    </div>
                </div>
            </div>
            {{ end }}
        </div>

        <!-- Pagination -->
        {{ if gt .Paginator.TotalPages 1 }}
        <div class="pagination-container" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
            <ul class="pagination">
                {{ if .Paginator.HasPrev }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Prev.URL }}" aria-label="Previous">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                </li>
                {{ end }}

                {{ $paginator := .Paginator }}
                {{ $adjacent_links := 2 }}
                {{ $max_links := (add (mul $adjacent_links 2) 1) }}
                {{ $lower_limit := (add $adjacent_links 1) }}
                {{ $upper_limit := (sub $paginator.TotalPages $adjacent_links) }}

                {{ if gt $paginator.TotalPages $max_links }}
                    {{ if le $paginator.PageNumber $lower_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if le .PageNumber $max_links }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else if ge $paginator.PageNumber $upper_limit }}
                        {{ range $paginator.Pagers }}
                            {{ if gt .PageNumber (sub $paginator.TotalPages $max_links) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ else }}
                        {{ range $paginator.Pagers }}
                            {{ if and (ge .PageNumber (sub $paginator.PageNumber $adjacent_links)) (le .PageNumber (add $paginator.PageNumber $adjacent_links)) }}
                                <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                                    <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                                </li>
                            {{ end }}
                        {{ end }}
                    {{ end }}
                {{ else }}
                    {{ range $paginator.Pagers }}
                        <li class="page-item{{ if eq . $paginator }} active{{ end }}">
                            <a class="page-link" href="{{ .URL }}">{{ .PageNumber }}</a>
                        </li>
                    {{ end }}
                {{ end }}

                {{ if .Paginator.HasNext }}
                <li class="page-item">
                    <a class="page-link" href="{{ .Paginator.Next.URL }}" aria-label="Next">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {{ else }}
                <li class="page-item disabled">
                    <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                </li>
                {{ end }}
            </ul>
        </div>
        {{ end }}
        {{ else }}
        <div class="empty-state" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
            <div class="empty-state-icon">
                <i class="fas fa-folder"></i>
            </div>
            <div class="empty-state-text">No posts found in this category</div>
            <a href="{{ "categories" | absURL }}" class="back-button">
                <i class="fas fa-arrow-left"></i> Back to All Categories
            </a>
        </div>
        {{ end }}
    </div>
</section>
{{ end }}
