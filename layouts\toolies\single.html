{{ define "head" }}
<meta name="description" content="Toolies - A collection of simple and useful tools">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/toolies-neo-cute.css" media="all">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<!-- fontawesome -->
<script defer src="{{ .Site.Params.staticPath }}/fontawesome-5/all-5.15.4.js"></script>
{{ end }}

{{ define "title" }}
Toolies | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<section class="toolies-container">
    <div class="container">
        <div class="text-center">
            <h1 class="toolies-title" data-aos="fade-up" data-aos-duration="800">Toolies</h1>
            <p class="toolies-description" data-aos="fade-up" data-aos-duration="800" data-aos-delay="100">
                A collection of simple and useful tools to help you with everyday tasks. From unit conversions to decision making and relaxation exercises.
            </p>
        </div>

        <div class="tool-cards">
            <!-- Positive Affirmations Generator -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="200">
                <div class="tool-card-header">
                    <i class="fas fa-sun tool-card-icon"></i>
                    <h2 class="tool-card-title">Positive Affirmations</h2>
                </div>
                <div class="tool-card-content">
                    <div class="affirmation-container">
                        <p class="affirmation-intro">Generate positive affirmations to boost your mindset:</p>
                        <div class="affirmation-categories">
                            <button type="button" class="affirmation-category-btn" data-category="confidence">Confidence</button>
                            <button type="button" class="affirmation-category-btn" data-category="abundance">Abundance</button>
                            <button type="button" class="affirmation-category-btn" data-category="health">Health</button>
                            <button type="button" class="affirmation-category-btn" data-category="relationships">Relationships</button>
                            <button type="button" class="affirmation-category-btn" data-category="success">Success</button>
                        </div>
                        <div id="affirmation-display" class="affirmation-display">
                            <p>Choose a category to generate an affirmation.</p>
                        </div>
                        <div class="affirmation-actions">
                            <button type="button" id="affirmation-save" class="affirmation-button">Save</button>
                            <button type="button" id="affirmation-copy" class="affirmation-button">Copy</button>
                        </div>
                        <div id="affirmation-saved" class="affirmation-saved">
                            <h3>Saved Affirmations</h3>
                            <div id="saved-affirmations-list" class="saved-affirmations-list">
                                <p class="no-saved-affirmations">No saved affirmations yet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Decision Helper -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="300">
                <div class="tool-card-header">
                    <i class="fas fa-random tool-card-icon"></i>
                    <h2 class="tool-card-title">Decision Helper</h2>
                </div>
                <div class="tool-card-content">
                    <form id="decision-form" class="decision-form">
                        <input type="text" id="decision-input" class="decision-input" placeholder="Enter an option">
                        <div id="decision-options" class="decision-options"></div>
                        <div class="decision-buttons">
                            <button type="button" id="decision-add" class="decision-add">Add Option</button>
                            <button type="button" id="decision-decide" class="decision-decide">Decide!</button>
                        </div>
                        <div id="decision-result" class="decision-result"></div>
                    </form>
                </div>
            </div>

            <!-- Breathing Exercise -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="400">
                <div class="tool-card-header">
                    <i class="fas fa-wind tool-card-icon"></i>
                    <h2 class="tool-card-title">Breathing Exercise</h2>
                </div>
                <div class="tool-card-content">
                    <div class="breathing-container">
                        <div id="breathing-circle" class="breathing-circle">Breathe</div>
                        <div id="breathing-text" class="breathing-text">Press Start to begin</div>
                        <div class="breathing-controls">
                            <button id="breathing-start" class="breathing-button">Start</button>
                            <button id="breathing-stop" class="breathing-button" disabled>Stop</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Meditation Timer -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="500">
                <div class="tool-card-header">
                    <i class="fas fa-om tool-card-icon"></i>
                    <h2 class="tool-card-title">Meditation Timer</h2>
                </div>
                <div class="tool-card-content">
                    <div class="meditation-container">
                        <div class="meditation-timer">
                            <span id="meditation-minutes">05</span>:<span id="meditation-seconds">00</span>
                        </div>
                        <div class="meditation-quote" id="meditation-quote">
                            "Quiet the mind, and the soul will speak."
                        </div>
                        <div class="meditation-controls">
                            <button id="meditation-start" class="meditation-button">Start</button>
                            <button id="meditation-stop" class="meditation-button" disabled>Stop</button>
                        </div>
                        <div class="meditation-duration">
                            <label for="meditation-duration-select">Duration:</label>
                            <select id="meditation-duration-select" class="meditation-select">
                                <option value="1">1 minute</option>
                                <option value="3">3 minutes</option>
                                <option value="5" selected>5 minutes</option>
                                <option value="10">10 minutes</option>
                                <option value="15">15 minutes</option>
                                <option value="20">20 minutes</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Gratitude Journal -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="600">
                <div class="tool-card-header">
                    <i class="fas fa-heart tool-card-icon"></i>
                    <h2 class="tool-card-title">Gratitude Journal</h2>
                </div>
                <div class="tool-card-content">
                    <div class="gratitude-container">
                        <p class="gratitude-intro">Write down three things you're grateful for today:</p>
                        <div class="gratitude-entries">
                            <div class="gratitude-entry">
                                <label>1.</label>
                                <input type="text" class="gratitude-input" id="gratitude-1" placeholder="I am grateful for...">
                            </div>
                            <div class="gratitude-entry">
                                <label>2.</label>
                                <input type="text" class="gratitude-input" id="gratitude-2" placeholder="I am grateful for...">
                            </div>
                            <div class="gratitude-entry">
                                <label>3.</label>
                                <input type="text" class="gratitude-input" id="gratitude-3" placeholder="I am grateful for...">
                            </div>
                        </div>
                        <button id="gratitude-save" class="gratitude-button">Save</button>
                        <div id="gratitude-message" class="gratitude-message"></div>
                    </div>
                </div>
            </div>

            <!-- Mood Tracker -->
            <div class="tool-card" data-aos="fade-up" data-aos-duration="800" data-aos-delay="700">
                <div class="tool-card-header">
                    <i class="fas fa-smile-beam tool-card-icon"></i>
                    <h2 class="tool-card-title">Mood Tracker</h2>
                </div>
                <div class="tool-card-content">
                    <div class="mood-container">
                        <p class="mood-question">How are you feeling today?</p>
                        <div class="mood-options">
                            <div class="mood-option" data-mood="amazing">
                                <span class="mood-emoji">😁</span>
                                <span class="mood-label">Amazing</span>
                            </div>
                            <div class="mood-option" data-mood="good">
                                <span class="mood-emoji">😊</span>
                                <span class="mood-label">Good</span>
                            </div>
                            <div class="mood-option" data-mood="okay">
                                <span class="mood-emoji">😐</span>
                                <span class="mood-label">Okay</span>
                            </div>
                            <div class="mood-option" data-mood="down">
                                <span class="mood-emoji">😔</span>
                                <span class="mood-label">Down</span>
                            </div>
                            <div class="mood-option" data-mood="stressed">
                                <span class="mood-emoji">😫</span>
                                <span class="mood-label">Stressed</span>
                            </div>
                        </div>
                        <div class="mood-note">
                            <label for="mood-note-input">Add a note (optional):</label>
                            <textarea id="mood-note-input" class="mood-note-input" placeholder="What's on your mind?"></textarea>
                        </div>
                        <button id="mood-save" class="mood-button">Save Mood</button>
                        <div id="mood-message" class="mood-message"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script src="{{ .Site.Params.staticPath }}/js/toolies.js"></script>
{{ end }}
