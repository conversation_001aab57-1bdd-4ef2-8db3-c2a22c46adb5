{{ define "head" }}
<meta name="description" content="{{ if .Params.description }}{{ .Params.description }}{{ else }}{{ .Summary }}{{ end }}">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/blog-neo-cute.css" media="all">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/table-neo-cute.css" media="all">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/toc-neo.css" media="all">
<!-- fontawesome -->
<script defer src="{{ .Site.Params.staticPath }}/fontawesome-5/all-5.15.4.js"></script>
{{ end }}

{{ define "title" }}
{{ .Title }} | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<div class="container">
    <div class="row justify-content-center">
        <!-- Mobile TOC (will be hidden on desktop) -->
        <div class="col-12 d-block d-lg-none mb-4">
            {{ if .Params.toc | default true}}
                <div class="toc-container mobile-toc">
                    <button class="toc-toggle collapsed">Table of Contents</button>
                    <div class="toc-content collapsed">
                        {{.TableOfContents}}
                    </div>
                </div>
            {{ end }}
        </div>

        <div class="col-sm-12 col-md-12 col-lg-9">
            <div class="blog-single">
                <div class="blog-single-header">
                    <h1 class="blog-single-title">{{ .Title }}</h1>
                    <div class="blog-single-meta">
                        <div class="blog-single-date">
                            <i class="far fa-calendar-alt"></i>
                            {{ .Date.Format (.Site.Params.datesFormat.article | default "Jan 2, 2006") }}
                        </div>
                        {{ if .Params.author }}
                        <div class="blog-single-author">
                            <i class="far fa-user"></i>
                            {{ .Params.author }}
                        </div>
                        {{ end }}
                        {{ if or (.Site.Params.singlePages.readTime.enable | default true) (.Params.enableReadingTime) }}
                        <div class="blog-single-readtime">
                            <i class="far fa-clock"></i>
                            <span id="readingTime">
                                {{ .Site.Params.singlePages.readTime.content | default "min read" }}
                            </span>
                        </div>
                        {{ end }}
                    </div>
                </div>

                {{ if .Params.image }}
                <div class="blog-single-image">
                    <img class="img-fluid" src="{{ .Params.image | relURL }}" alt="{{ .Title }}">
                </div>
                {{ end }}

                <div class="blog-single-content">
                    {{ .Content | emojify }}

                    {{ if .Params.buttons }}
                    <div class="blog-single-buttons">
                        {{ range .Params.buttons }}
                        <a href="{{ absURL .url }}" class="blog-single-button">{{ .label }}</a>
                        {{ end }}
                    </div>
                    {{ end }}
                </div>

                {{ if .Params.tags }}
                <div class="blog-single-tags">
                    {{ range .Params.tags }}
                    <a href="{{ "tags" | absURL }}/{{ . | urlize }}" class="blog-single-tag">
                        <i class="fas fa-tag"></i>
                        {{ . }}
                    </a>
                    {{ end }}
                </div>
                {{ end }}
            </div>

            {{ template "_internal/disqus.html" . }}
        </div>

        <div class="col-sm-12 col-md-12 col-lg-3">
            <div class="sticky-sidebar">
                {{ if .Params.toc | default true}}
                <aside class="toc">
                    <h5>
                        {{ .Site.Params.terms.toc | default "Table Of Contents" }}
                    </h5>
                    <div class="toc-content">
                        {{.TableOfContents}}
                    </div>
                </aside>
                {{ end }}

                {{ if .Params.quiz }}
                <div class="row">
                    <aside class="toc">
                        <div class="toc-content">
                            {{ partial "wordguess.html" . }}
                            <script>
                                var quizCode = "{{ with .Params.quiz }}{{ .code }}{{ end }}";
                            </script>
                        </div>
                        <script src="{{ "js/wordguess.js" | relURL }}?code={{ .Params.quiz.code }}"></script>
                    </aside>
                </div>
                {{ end }}

                {{ if .Params.wordfill }}
                <div class="row">
                    <aside class="toc">
                        <div class="toc-content">
                            {{ partial "wordfill.html" . }}
                            <script>
                                var wordCode = "{{ with .Params.wordfill }}{{ .code }}{{ end }}";
                            </script>
                        </div>
                        <script src="{{ "js/wordfill.js" | relURL }}?code={{ .Params.wordfill.code }}"></script>
                    </aside>
                </div>
                {{ end }}

                {{ if .Params.socialShare | default true }}
                <aside class="social">
                    <h5>{{ .Site.Params.terms.social | default "Social" }}</h5>
                    <div class="social-content">
                        <ul class="list-inline">
                            <li class="list-inline-item text-center">
                                <a target="_blank" href="https://twitter.com/share?text={{ .Title }}&url={{ .Site.Params.hostName }}{{ .Permalink | absURL }}">
                                    <i class="fab fa-twitter"></i>
                                </a>
                            </li>
                            <li class="list-inline-item text-center">
                                <a target="_blank" href="https://api.whatsapp.com/send?text={{ .Title }}: {{ .Site.Params.hostName }}{{ .Permalink | absURL }}">
                                    <i class="fab fa-whatsapp"></i>
                                </a>
                            </li>
                            <li class="list-inline-item text-center">
                                <a target="_blank" href='mailto:?subject={{ .Title }}&amp;body={{ .Site.Params.terms.emailText | default "Check out this site" }} {{ .Site.Params.hostName }}{{ .Permalink | absURL }}'>
                                    <i class="fa fa-envelope"></i>
                                </a>
                            </li>
                        </ul>
                    </div>
                </aside>
                {{ end }}
            </div>
        </div>
    </div>
</div>

<button class="p-2 px-3" onclick="topFunction()" id="topScroll">
    <i class="fas fa-angle-up"></i>
</button>

{{ if or (.Site.Params.singlePages.scrollprogress.enable | default true) (.Params.enableScrollProgress) }}
<div class="progress">
    <div id="scroll-progress-bar" class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
</div>
<Script src="{{ .Site.Params.staticPath }}/js/scrollProgressBar.js"></script>
{{ end }}

<script>
    var topScroll = document.getElementById("topScroll");
    window.onscroll = function() {scrollFunction()};

    function scrollFunction() {
        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            topScroll.style.display = "block";
        } else {
            topScroll.style.display = "none";
        }
    }

    function topFunction() {
        document.body.scrollTop = 0;
        document.documentElement.scrollTop = 0;
    }
</script>

{{ if or (.Site.Params.singlePages.readTime.enable | default true) (.Params.enableReadingTime) }}
<script src="{{ .Site.Params.staticPath }}/js/readingTime.js"></script>
{{ end }}

<!-- Simple TOC toggle script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get all TOC toggle buttons
        var tocToggles = document.querySelectorAll('.toc-toggle');

        // Add click event to each toggle
        tocToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function() {
                // Toggle collapsed class on the button
                this.classList.toggle('collapsed');
                this.classList.toggle('active');

                // Get the next sibling which is the content
                var content = this.nextElementSibling;
                content.classList.toggle('collapsed');
                content.classList.toggle('active');
            });
        });
    });
</script>
{{ end }}
