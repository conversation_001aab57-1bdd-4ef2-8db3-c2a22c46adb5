/* Neo-brutalism Habit Tracker Styles with Cute Elements */

/* Habit tracker section */
#habit-tracker {
    position: relative;
    padding: 60px 0;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark #habit-tracker {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
#habit-tracker::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#FFD1DC 6px, transparent 6px),
        radial-gradient(#FFD1DC 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark #habit-tracker::before {
    background-image:
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(255, 209, 220, 0.3) 6px, transparent 6px);
}

/* Habit tracker container */
.habits-container {
    position: relative;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    padding: 30px;
    margin-bottom: 30px;
    z-index: 1;
}

.dark .habits-container {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

/* User profile section */
.user-profile {
    position: relative;
    margin-bottom: 30px;
    z-index: 1;
}

.user-profile .card {
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    padding: 0;
    overflow: hidden;
}

.dark .user-profile .card {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.user-profile .card-body {
    padding: 30px;
}

.user-profile h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 20px;
    font-family: 'Playfair Display', serif;
}

.dark .user-profile h3 {
    color: #fff;
}

/* Form controls */
.form-control {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 10px 15px;
    color: #000;
    margin-bottom: 10px;
}

.dark .form-control {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.form-control:focus {
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    border-color: #000;
}

.dark .form-control:focus {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.7);
}

/* Buttons */
.btn-primary,
button.btn-primary {
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 10px 15px;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    cursor: pointer;
}

.dark .btn-primary,
.dark button.btn-primary {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.btn-primary:hover,
button.btn-primary:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    background-color: #FFD1DC;
    color: #000;
}

.dark .btn-primary:hover,
.dark button.btn-primary:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    background-color: rgba(255, 209, 220, 0.3);
    color: #fff;
}

.btn-success,
button.btn-success {
    background-color: #A2D2FF;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    cursor: pointer;
    display: inline-block;
    text-decoration: none;
    font-size: 0.9rem;
}

.dark .btn-success,
.dark button.btn-success {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.btn-success:hover,
button.btn-success:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    background-color: #A2D2FF;
    color: #000;
    text-decoration: none;
}

.dark .btn-success:hover,
.dark button.btn-success:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    background-color: rgba(162, 210, 255, 0.3);
    color: #fff;
    text-decoration: none;
}

/* Fix for disabled buttons */
.btn-success:disabled,
button.btn-success:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.3);
}

.dark .btn-success:disabled,
.dark button.btn-success:disabled {
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.1);
}

/* Add habit section */
.add-habit-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
}

.dark .add-habit-section {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

/* Habit item */
.habit-item {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .habit-item {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

.habit-item:hover {
    transform: translate(-3px, -3px);
    box-shadow: 7px 7px 0 rgba(0, 0, 0, 0.7);
}

.dark .habit-item:hover {
    box-shadow: 7px 7px 0 rgba(255, 255, 255, 0.15);
}

.habit-item.completed {
    background-color: #f0f9ff;
    border-color: #A2D2FF;
}

.dark .habit-item.completed {
    background-color: rgba(162, 210, 255, 0.1);
    border-color: rgba(162, 210, 255, 0.5);
}

/* Habit content */
.habit-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.habit-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #000;
}

.dark .habit-name {
    color: #fff;
}

/* Progress cards */
.progress-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.stat-card {
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    padding: 20px;
    text-align: center;
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .stat-card {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
}

.stat-card:hover {
    transform: translate(-2px, -2px);
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .stat-card:hover {
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.stat-card i {
    font-size: 2rem;
    color: #A2D2FF;
    margin-bottom: 10px;
}

.dark .stat-card i {
    color: rgba(162, 210, 255, 0.7);
}

.stat-card span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #000;
    display: block;
    margin: 10px 0;
}

.dark .stat-card span {
    color: #fff;
}

.stat-card p {
    margin: 0;
    color: #555;
    font-size: 0.9rem;
}

.dark .stat-card p {
    color: rgba(255, 255, 255, 0.7);
}

/* Achievement items */
.achievement-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 10px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .achievement-item {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.achievement-item:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .achievement-item:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.achievement-item.locked {
    opacity: 0.6;
    background-color: #f5f5f5;
}

.dark .achievement-item.locked {
    background-color: #222;
}

.achievement-icon {
    font-size: 1.5rem;
    margin-right: 15px;
}

.achievement-details {
    display: flex;
    flex-direction: column;
}

.achievement-details strong {
    color: #000;
    font-size: 1rem;
}

.dark .achievement-details strong {
    color: #fff;
}

.achievement-details small {
    color: #555;
    font-size: 0.85rem;
}

.dark .achievement-details small {
    color: rgba(255, 255, 255, 0.7);
}

/* Token balance */
.token-balance {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background-color: #FFD1DC;
    color: #000;
    border: 2px solid #000;
    border-radius: 0;
    padding: 8px 15px;
    font-weight: 600;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    margin-top: 10px;
}

.dark .token-balance {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.token-balance i {
    color: #ffd700;
}

/* Cute decorative elements */
#habit-tracker::after {
    content: "♥";
    position: absolute;
    bottom: 20px;
    right: 20px;
    font-size: 2rem;
    color: #FF6B8B;
    z-index: 0;
}

.dark #habit-tracker::after {
    color: rgba(255, 107, 139, 0.7);
}

.habits-container::before {
    content: "♥";
    position: absolute;
    top: -15px;
    left: -15px;
    font-size: 1.5rem;
    color: #FF6B8B;
    z-index: 2;
}

.dark .habits-container::before {
    color: rgba(255, 107, 139, 0.7);
}

/* Responsive design */
@media (max-width: 991px) {
    #habit-tracker {
        margin: 30px 15px;
        padding: 40px 0;
    }

    .habits-container {
        padding: 20px;
    }

    .progress-cards {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }
}

@media (max-width: 768px) {
    .user-profile .card-body {
        padding: 20px;
    }

    .habit-item {
        padding: 10px;
    }

    .habit-name {
        font-size: 1rem;
    }

    .btn-success {
        padding: 4px 8px;
        font-size: 0.9rem;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-card i {
        font-size: 1.5rem;
    }

    .stat-card span {
        font-size: 1.2rem;
    }
}

@media (max-width: 576px) {
    #habit-tracker {
        margin: 20px 10px;
        padding: 30px 0;
    }

    .habits-container {
        padding: 15px;
    }

    .progress-cards {
        grid-template-columns: 1fr 1fr;
    }

    .achievement-item {
        padding: 8px 10px;
    }

    .achievement-icon {
        font-size: 1.2rem;
        margin-right: 10px;
    }
}
