{{ define "head" }}
<meta name="description" content="Page not found">
<link rel="stylesheet" href="{{ .Site.Params.staticPath }}/css/404-neo-cute.css" media="all">
{{ end }}

{{ define "title" }}
404 - Page Not Found | {{ .Site.Title }}
{{ end }}

{{ define "main" }}
<div class="error-page">
    <!-- Floating decorative elements -->
    <div class="floating-item item1 heart">♥</div>
    <div class="floating-item item2 star">★</div>
    <div class="floating-item item3 circle"></div>
    <div class="floating-item item4 square"></div>
    <div class="floating-item item5 triangle"></div>
    <div class="floating-item item6 cloud">☁️</div>
    <div class="floating-item item7 flower">🌸</div>
    <div class="floating-item item8 star">✨</div>

    <div class="error-container">
        <div class="error-character">
            <div class="character-container" id="character">
                <div class="character-head">
                    <div class="character-face">
                        <div class="character-eyes">
                            <div class="character-eye left"></div>
                            <div class="character-eye right"></div>
                        </div>
                        <div class="character-blush left"></div>
                        <div class="character-blush right"></div>
                        <div class="character-mouth"></div>
                    </div>
                </div>
                <div class="character-pigtail left"></div>
                <div class="character-pigtail right"></div>
                <div class="character-body"></div>
                <div class="character-arm left"></div>
                <div class="character-arm right"></div>
                <div class="character-leg left"></div>
                <div class="character-leg right"></div>
            </div>
        </div>

        <div class="error-speech-bubble" id="speechBubble">
            <p>Hi! I'm Shreya! Click me!</p>
        </div>

        <h1 class="error-title">404</h1>
        <h2 class="error-message">Oops! Page Not Found</h2>
        <p class="error-description">
            The page you're looking for seems to have wandered off. But don't be sad, SHREYA to the rescue! Follow my socials to find your way back home!
        </p>

        <div class="error-buttons">
            <button class="home-button" id="playButton">Play Tic-Tac-Toe</button>
            <a href="{{ .Site.BaseURL }}" class="home-button">Go Home</a>
            <button class="home-button" id="dressUpButton">Dress Up Shreya!</button>
        </div>

        <!-- Dress up game container -->
        <div class="dressup-container" id="dressUpContainer">
            <div class="dressup-header">
                <h3 class="dressup-title">Dress Up Shreya!</h3>
                <p class="dressup-description">Mix and match outfits, hairstyles, and accessories to create your perfect look!</p>
            </div>

            <div class="dressup-content">
                <div class="dressup-options-container">
                    <div class="dressup-tabs">
                        <button class="dressup-tab active" data-tab="hairstyle">Hair</button>
                        <button class="dressup-tab" data-tab="outfit">Outfit</button>
                        <button class="dressup-tab" data-tab="accessories">Accessories</button>
                        <button class="dressup-tab" data-tab="expression">Expression</button>
                        <button class="dressup-tab" data-tab="extras">Extras</button>
                    </div>

                    <div class="dressup-tab-content active" id="hairstyle-tab">
                        <div class="dressup-options">
                            <div class="dressup-category">
                                <h4>Hair Style</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="hairstyle" data-style="pigtails">
                                        <div class="accessory-icon">🎀</div>
                                        <span class="item-label">Pigtails</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="ponytail">
                                        <div class="accessory-icon">👱‍♀️</div>
                                        <span class="item-label">Ponytail</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="short">
                                        <div class="accessory-icon">💇‍♀️</div>
                                        <span class="item-label">Short</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="long">
                                        <div class="accessory-icon">👸</div>
                                        <span class="item-label">Long</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="curly">
                                        <div class="accessory-icon">👩‍🦱</div>
                                        <span class="item-label">Curly</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="wavy">
                                        <div class="accessory-icon">🧜‍♀️</div>
                                        <span class="item-label">Wavy</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="bun">
                                        <div class="accessory-icon">👩‍🦰</div>
                                        <span class="item-label">Bun</span>
                                    </div>
                                    <div class="dressup-item" data-type="hairstyle" data-style="braids">
                                        <div class="accessory-icon">👧</div>
                                        <span class="item-label">Braids</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Hair Color</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="hair" data-color="#8B4513">
                                        <div class="color-swatch" style="background-color: #8B4513;"></div>
                                        <span class="item-label">Brown</span>
                                    </div>
                                    <div class="dressup-item" data-type="hair" data-color="#FFD700">
                                        <div class="color-swatch" style="background-color: #FFD700;"></div>
                                        <span class="item-label">Blonde</span>
                                    </div>
                                    <div class="dressup-item" data-type="hair" data-color="#FF69B4">
                                        <div class="color-swatch" style="background-color: #FF69B4;"></div>
                                        <span class="item-label">Pink</span>
                                    </div>
                                    <div class="dressup-item" data-type="hair" data-color="#000000">
                                        <div class="color-swatch" style="background-color: #000000;"></div>
                                        <span class="item-label">Black</span>
                                    </div>
                                    <div class="dressup-item" data-type="hair" data-color="#FF0000">
                                        <div class="color-swatch" style="background-color: #FF0000;"></div>
                                        <span class="item-label">Red</span>
                                    </div>
                                    <div class="dressup-item" data-type="hair" data-color="#800080">
                                        <div class="color-swatch" style="background-color: #800080;"></div>
                                        <span class="item-label">Purple</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dressup-tab-content" id="outfit-tab">
                        <div class="dressup-options">
                            <div class="dressup-category">
                                <h4>Outfit Style</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="outfit" data-style="dress">
                                        <div class="accessory-icon">👗</div>
                                        <span class="item-label">Dress</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="uniform">
                                        <div class="accessory-icon">🏫</div>
                                        <span class="item-label">Uniform</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="casual">
                                        <div class="accessory-icon">👚</div>
                                        <span class="item-label">Casual</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="princess">
                                        <div class="accessory-icon">👑</div>
                                        <span class="item-label">Princess</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="swimsuit">
                                        <div class="accessory-icon">🩱</div>
                                        <span class="item-label">Swimsuit</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="party">
                                        <div class="accessory-icon">🎉</div>
                                        <span class="item-label">Party</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="pajamas">
                                        <div class="accessory-icon">🌙</div>
                                        <span class="item-label">Pajamas</span>
                                    </div>
                                    <div class="dressup-item" data-type="outfit" data-style="sporty">
                                        <div class="accessory-icon">🏃‍♀️</div>
                                        <span class="item-label">Sporty</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Outfit Color</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="dress" data-color="#A2D2FF">
                                        <div class="color-swatch" style="background-color: #A2D2FF;"></div>
                                        <span class="item-label">Blue</span>
                                    </div>
                                    <div class="dressup-item" data-type="dress" data-color="#FFD1DC">
                                        <div class="color-swatch" style="background-color: #FFD1DC;"></div>
                                        <span class="item-label">Pink</span>
                                    </div>
                                    <div class="dressup-item" data-type="dress" data-color="#B5EAD7">
                                        <div class="color-swatch" style="background-color: #B5EAD7;"></div>
                                        <span class="item-label">Green</span>
                                    </div>
                                    <div class="dressup-item" data-type="dress" data-color="#C7CEEA">
                                        <div class="color-swatch" style="background-color: #C7CEEA;"></div>
                                        <span class="item-label">Purple</span>
                                    </div>
                                    <div class="dressup-item" data-type="dress" data-color="#FDFD96">
                                        <div class="color-swatch" style="background-color: #FDFD96;"></div>
                                        <span class="item-label">Yellow</span>
                                    </div>
                                    <div class="dressup-item" data-type="dress" data-color="#FF6961">
                                        <div class="color-swatch" style="background-color: #FF6961;"></div>
                                        <span class="item-label">Red</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dressup-tab-content" id="accessories-tab">
                        <div class="dressup-options">
                            <div class="dressup-category">
                                <h4>Head Accessories</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item" data-type="head-accessory" data-accessory="bow">
                                        <div class="accessory-icon">🎀</div>
                                        <span class="item-label">Bow</span>
                                    </div>
                                    <div class="dressup-item" data-type="head-accessory" data-accessory="hat">
                                        <div class="accessory-icon">👒</div>
                                        <span class="item-label">Hat</span>
                                    </div>
                                    <div class="dressup-item" data-type="head-accessory" data-accessory="crown">
                                        <div class="accessory-icon">👑</div>
                                        <span class="item-label">Crown</span>
                                    </div>
                                    <div class="dressup-item" data-type="head-accessory" data-accessory="flower">
                                        <div class="accessory-icon">🌸</div>
                                        <span class="item-label">Flower</span>
                                    </div>
                                    <div class="dressup-item" data-type="head-accessory" data-accessory="headband">
                                        <div class="accessory-icon">📿</div>
                                        <span class="item-label">Headband</span>
                                    </div>
                                    <div class="dressup-item active" data-type="head-accessory" data-accessory="none">
                                        <div class="accessory-icon">❌</div>
                                        <span class="item-label">None</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Face Accessories</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item" data-type="face-accessory" data-accessory="glasses">
                                        <div class="accessory-icon">👓</div>
                                        <span class="item-label">Glasses</span>
                                    </div>
                                    <div class="dressup-item" data-type="face-accessory" data-accessory="sunglasses">
                                        <div class="accessory-icon">🕶️</div>
                                        <span class="item-label">Sunglasses</span>
                                    </div>
                                    <div class="dressup-item" data-type="face-accessory" data-accessory="mask">
                                        <div class="accessory-icon">😷</div>
                                        <span class="item-label">Mask</span>
                                    </div>
                                    <div class="dressup-item active" data-type="face-accessory" data-accessory="none">
                                        <div class="accessory-icon">❌</div>
                                        <span class="item-label">None</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Neck Accessories</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item" data-type="neck-accessory" data-accessory="necklace">
                                        <div class="accessory-icon">📿</div>
                                        <span class="item-label">Necklace</span>
                                    </div>
                                    <div class="dressup-item" data-type="neck-accessory" data-accessory="scarf">
                                        <div class="accessory-icon">🧣</div>
                                        <span class="item-label">Scarf</span>
                                    </div>
                                    <div class="dressup-item" data-type="neck-accessory" data-accessory="bowtie">
                                        <div class="accessory-icon">🎀</div>
                                        <span class="item-label">Bowtie</span>
                                    </div>
                                    <div class="dressup-item active" data-type="neck-accessory" data-accessory="none">
                                        <div class="accessory-icon">❌</div>
                                        <span class="item-label">None</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Hand Accessories</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item" data-type="hand-accessory" data-accessory="bracelet">
                                        <div class="accessory-icon">💎</div>
                                        <span class="item-label">Bracelet</span>
                                    </div>
                                    <div class="dressup-item" data-type="hand-accessory" data-accessory="gloves">
                                        <div class="accessory-icon">🧤</div>
                                        <span class="item-label">Gloves</span>
                                    </div>
                                    <div class="dressup-item" data-type="hand-accessory" data-accessory="ring">
                                        <div class="accessory-icon">💍</div>
                                        <span class="item-label">Ring</span>
                                    </div>
                                    <div class="dressup-item active" data-type="hand-accessory" data-accessory="none">
                                        <div class="accessory-icon">❌</div>
                                        <span class="item-label">None</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dressup-tab-content" id="expression-tab">
                        <div class="dressup-options">
                            <div class="dressup-category">
                                <h4>Expression</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="expression" data-expression="happy">
                                        <div class="accessory-icon">😊</div>
                                        <span class="item-label">Happy</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="sad">
                                        <div class="accessory-icon">😢</div>
                                        <span class="item-label">Sad</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="surprised">
                                        <div class="accessory-icon">😮</div>
                                        <span class="item-label">Surprised</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="waving">
                                        <div class="accessory-icon">👋</div>
                                        <span class="item-label">Waving</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="excited">
                                        <div class="accessory-icon">🤩</div>
                                        <span class="item-label">Excited</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="sleepy">
                                        <div class="accessory-icon">😴</div>
                                        <span class="item-label">Sleepy</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="angry">
                                        <div class="accessory-icon">😠</div>
                                        <span class="item-label">Angry</span>
                                    </div>
                                    <div class="dressup-item" data-type="expression" data-expression="silly">
                                        <div class="accessory-icon">🤪</div>
                                        <span class="item-label">Silly</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dressup-tab-content" id="extras-tab">
                        <div class="dressup-options">
                            <div class="dressup-category">
                                <h4>Backgrounds</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="background" data-bg="none">
                                        <div class="accessory-icon">⬜</div>
                                        <span class="item-label">None</span>
                                    </div>
                                    <div class="dressup-item" data-type="background" data-bg="beach">
                                        <div class="accessory-icon">🏖️</div>
                                        <span class="item-label">Beach</span>
                                    </div>
                                    <div class="dressup-item" data-type="background" data-bg="park">
                                        <div class="accessory-icon">🌳</div>
                                        <span class="item-label">Park</span>
                                    </div>
                                    <div class="dressup-item" data-type="background" data-bg="school">
                                        <div class="accessory-icon">🏫</div>
                                        <span class="item-label">School</span>
                                    </div>
                                    <div class="dressup-item" data-type="background" data-bg="party">
                                        <div class="accessory-icon">🎉</div>
                                        <span class="item-label">Party</span>
                                    </div>
                                    <div class="dressup-item" data-type="background" data-bg="space">
                                        <div class="accessory-icon">🌌</div>
                                        <span class="item-label">Space</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Footwear</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="footwear" data-style="default">
                                        <div class="accessory-icon">👞</div>
                                        <span class="item-label">Default</span>
                                    </div>
                                    <div class="dressup-item" data-type="footwear" data-style="boots">
                                        <div class="accessory-icon">👢</div>
                                        <span class="item-label">Boots</span>
                                    </div>
                                    <div class="dressup-item" data-type="footwear" data-style="sneakers">
                                        <div class="accessory-icon">👟</div>
                                        <span class="item-label">Sneakers</span>
                                    </div>
                                    <div class="dressup-item" data-type="footwear" data-style="sandals">
                                        <div class="accessory-icon">👡</div>
                                        <span class="item-label">Sandals</span>
                                    </div>
                                    <div class="dressup-item" data-type="footwear" data-style="heels">
                                        <div class="accessory-icon">👠</div>
                                        <span class="item-label">Heels</span>
                                    </div>
                                </div>
                            </div>

                            <div class="dressup-category">
                                <h4>Props</h4>
                                <div class="dressup-items">
                                    <div class="dressup-item active" data-type="prop" data-prop="none">
                                        <div class="accessory-icon">❌</div>
                                        <span class="item-label">None</span>
                                    </div>
                                    <div class="dressup-item" data-type="prop" data-prop="balloon">
                                        <div class="accessory-icon">🎈</div>
                                        <span class="item-label">Balloon</span>
                                    </div>
                                    <div class="dressup-item" data-type="prop" data-prop="book">
                                        <div class="accessory-icon">📚</div>
                                        <span class="item-label">Book</span>
                                    </div>
                                    <div class="dressup-item" data-type="prop" data-prop="umbrella">
                                        <div class="accessory-icon">☂️</div>
                                        <span class="item-label">Umbrella</span>
                                    </div>
                                    <div class="dressup-item" data-type="prop" data-prop="icecream">
                                        <div class="accessory-icon">🍦</div>
                                        <span class="item-label">Ice Cream</span>
                                    </div>
                                    <div class="dressup-item" data-type="prop" data-prop="gift">
                                        <div class="accessory-icon">🎁</div>
                                        <span class="item-label">Gift</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="dressup-footer">
                <button class="dressup-button dressup-random" id="randomLookButton">Random Look</button>
                <button class="dressup-button" id="closeDressUpButton">Close</button>
                <button class="dressup-button dressup-reset" id="resetLookButton">Reset</button>
            </div>

            <div id="confettiContainer"></div>
        </div>

        <!-- Interactive game container -->
        <div class="game-container" id="gameContainer">
            <h3 class="game-title">Tic-Tac-Toe</h3>
            <p class="game-description">While you're here, let's play a quick game!</p>

            <div class="game-board" id="gameBoard">
                <div class="game-cell" data-index="0"></div>
                <div class="game-cell" data-index="1"></div>
                <div class="game-cell" data-index="2"></div>
                <div class="game-cell" data-index="3"></div>
                <div class="game-cell" data-index="4"></div>
                <div class="game-cell" data-index="5"></div>
                <div class="game-cell" data-index="6"></div>
                <div class="game-cell" data-index="7"></div>
                <div class="game-cell" data-index="8"></div>
            </div>

            <div class="game-status" id="gameStatus">Your turn (X)</div>
            <button class="game-button" id="resetButton">Reset Game</button>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Character interaction
        const character = document.getElementById('character');
        const speechBubble = document.getElementById('speechBubble');
        const expressions = ['happy', 'sad', 'surprised', 'waving'];
        const messages = [
            'Hi there! I\'m feeling happy today!',
            'Oh no! I can\'t find that page...',
            'Wow! I didn\'t expect that!',
            'Hello! Nice to meet you!'
        ];
        let currentExpression = '';

        character.addEventListener('click', function() {
            // Remove current expression
            character.classList.remove(currentExpression);

            // Get random new expression
            let expressionIndex;
            do {
                expressionIndex = Math.floor(Math.random() * expressions.length);
                newExpression = expressions[expressionIndex];
            } while (newExpression === currentExpression);

            currentExpression = newExpression;
            character.classList.add(currentExpression);

            // Update speech bubble
            speechBubble.querySelector('p').textContent = messages[expressionIndex];

            // Animate speech bubble
            speechBubble.style.animation = 'none';
            void speechBubble.offsetWidth; // Trigger reflow
            speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
        });

        // Automatic expression change every 5 seconds
        setInterval(function() {
            if (Math.random() > 0.6) { // 40% chance to change expression
                character.click();
            }
        }, 5000);

        // Eye following mouse
        document.addEventListener('mousemove', function(e) {
            const eyes = document.querySelectorAll('.character-eye');
            const mouseX = e.clientX;
            const mouseY = e.clientY;

            eyes.forEach(eye => {
                const eyeRect = eye.getBoundingClientRect();
                const eyeCenterX = eyeRect.left + eyeRect.width / 2;
                const eyeCenterY = eyeRect.top + eyeRect.height / 2;

                const angle = Math.atan2(mouseY - eyeCenterY, mouseX - eyeCenterX);
                const distance = Math.min(2, Math.sqrt(Math.pow(mouseX - eyeCenterX, 2) + Math.pow(mouseY - eyeCenterY, 2)) / 50);

                const moveX = Math.cos(angle) * distance;
                const moveY = Math.sin(angle) * distance;

                eye.style.transform = `translate(${moveX}px, ${moveY}px)`;
            });
        });

        // Dress Up Game
        const dressUpButton = document.getElementById('dressUpButton');
        const dressUpContainer = document.getElementById('dressUpContainer');
        const closeDressUpButton = document.getElementById('closeDressUpButton');
        const dressUpItems = document.querySelectorAll('.dressup-item');
        const dressupTabs = document.querySelectorAll('.dressup-tab');
        const dressupTabContents = document.querySelectorAll('.dressup-tab-content');
        const randomLookButton = document.getElementById('randomLookButton');
        const resetLookButton = document.getElementById('resetLookButton');
        // We'll use the main character directly
        const confettiContainer = document.getElementById('confettiContainer');

        // Current accessories state
        let currentHeadAccessory = 'none';
        let currentFaceAccessory = 'none';
        let currentNeckAccessory = 'none';
        let currentHandAccessory = 'none';
        let currentFootwear = 'default';
        let currentBackground = 'none';
        let currentProp = 'none';

        // Accessory elements
        let headAccessoryElement = null;
        let faceAccessoryElement = null;
        let neckAccessoryElement = null;
        let handAccessoryElement = null;
        let propElement = null;
        let backgroundElement = null;

        // Tab switching functionality
        dressupTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                dressupTabs.forEach(t => t.classList.remove('active'));
                dressupTabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab and corresponding content
                this.classList.add('active');
                const tabId = this.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });

        dressUpButton.addEventListener('click', function() {
            dressUpContainer.style.display = 'block';
            gameContainer.style.display = 'none';

            // Change character expression to happy
            character.classList.remove(currentExpression);
            currentExpression = 'happy';
            character.classList.add(currentExpression);

            // Update speech bubble
            speechBubble.querySelector('p').textContent = 'Yay! Let\'s play dress up!';
            speechBubble.style.animation = 'none';
            void speechBubble.offsetWidth; // Trigger reflow
            speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
        });

        closeDressUpButton.addEventListener('click', function() {
            dressUpContainer.style.display = 'none';
        });

        // Handle dress up item clicks
        dressUpItems.forEach(item => {
            item.addEventListener('click', function() {
                const type = this.getAttribute('data-type');

                // Remove active class from other items in the same category
                document.querySelectorAll(`.dressup-item[data-type="${type}"]`).forEach(el => {
                    el.classList.remove('active');
                });

                // Add active class to clicked item
                this.classList.add('active');

                if (type === 'hair') {
                    const color = this.getAttribute('data-color');
                    changeHairColor(color);
                } else if (type === 'dress') {
                    const color = this.getAttribute('data-color');
                    changeDressColor(color);
                } else if (type === 'head-accessory') {
                    const accessory = this.getAttribute('data-accessory');
                    changeHeadAccessory(accessory);
                } else if (type === 'face-accessory') {
                    const accessory = this.getAttribute('data-accessory');
                    changeFaceAccessory(accessory);
                } else if (type === 'neck-accessory') {
                    const accessory = this.getAttribute('data-accessory');
                    changeNeckAccessory(accessory);
                } else if (type === 'hand-accessory') {
                    const accessory = this.getAttribute('data-accessory');
                    changeHandAccessory(accessory);
                } else if (type === 'hairstyle') {
                    const style = this.getAttribute('data-style');
                    changeHairStyle(style);
                } else if (type === 'outfit') {
                    const style = this.getAttribute('data-style');
                    changeOutfitStyle(style);
                } else if (type === 'expression') {
                    const expression = this.getAttribute('data-expression');
                    changeExpression(expression);
                } else if (type === 'footwear') {
                    const style = this.getAttribute('data-style');
                    changeFootwear(style);
                } else if (type === 'background') {
                    const bg = this.getAttribute('data-bg');
                    changeBackground(bg);
                } else if (type === 'prop') {
                    const prop = this.getAttribute('data-prop');
                    changeProp(prop);
                }

                // Update speech bubble with a random compliment
                const compliments = [
                    'I love this new look!',
                    'This looks so cute on me!',
                    'What a great choice!',
                    'I feel so pretty now!',
                    'This is my new favorite style!',
                    'You have great taste!',
                    'I\'m feeling fabulous!',
                    'This is perfect for me!',
                    'I look amazing, thank you!',
                    'This style suits me so well!'
                ];
                const randomCompliment = compliments[Math.floor(Math.random() * compliments.length)];
                speechBubble.querySelector('p').textContent = randomCompliment;
                speechBubble.style.animation = 'none';
                void speechBubble.offsetWidth; // Trigger reflow
                speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
            });
        });

        // Random look button
        randomLookButton.addEventListener('click', function() {
            // Create confetti effect
            createConfetti();

            // Select random items from each category
            const categories = [
                'hairstyle', 'hair', 'outfit', 'dress',
                'head-accessory', 'face-accessory', 'neck-accessory', 'hand-accessory',
                'expression', 'footwear', 'background', 'prop'
            ];

            categories.forEach(category => {
                const items = Array.from(document.querySelectorAll(`.dressup-item[data-type="${category}"]`));
                if (items.length > 0) {
                    const randomItem = items[Math.floor(Math.random() * items.length)];
                    randomItem.click();
                }
            });

            // Update speech bubble
            speechBubble.querySelector('p').textContent = 'Wow! A totally random look!';
            speechBubble.style.animation = 'none';
            void speechBubble.offsetWidth;
            speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
        });

        // Reset look button
        resetLookButton.addEventListener('click', function() {
            // Click the first item in each category to reset to defaults
            document.querySelector('.dressup-item[data-type="hairstyle"][data-style="pigtails"]').click();
            document.querySelector('.dressup-item[data-type="hair"][data-color="#8B4513"]').click();
            document.querySelector('.dressup-item[data-type="outfit"][data-style="dress"]').click();
            document.querySelector('.dressup-item[data-type="dress"][data-color="#A2D2FF"]').click();
            document.querySelector('.dressup-item[data-type="head-accessory"][data-accessory="none"]').click();
            document.querySelector('.dressup-item[data-type="face-accessory"][data-accessory="none"]').click();
            document.querySelector('.dressup-item[data-type="neck-accessory"][data-accessory="none"]').click();
            document.querySelector('.dressup-item[data-type="hand-accessory"][data-accessory="none"]').click();
            document.querySelector('.dressup-item[data-type="expression"][data-expression="happy"]').click();
            document.querySelector('.dressup-item[data-type="footwear"][data-style="default"]').click();
            document.querySelector('.dressup-item[data-type="background"][data-bg="none"]').click();
            document.querySelector('.dressup-item[data-type="prop"][data-prop="none"]').click();

            // Update speech bubble
            speechBubble.querySelector('p').textContent = 'Back to my original look!';
            speechBubble.style.animation = 'none';
            void speechBubble.offsetWidth;
            speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
        });

        // Create confetti effect
        function createConfetti() {
            confettiContainer.innerHTML = '';

            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.className = 'confetti';
                confetti.style.left = `${Math.random() * 100}%`;
                confetti.style.animationDelay = `${Math.random() * 2}s`;
                confetti.style.animationDuration = `${2 + Math.random() * 2}s`;
                confettiContainer.appendChild(confetti);
            }

            // Remove confetti after animation completes
            setTimeout(() => {
                confettiContainer.innerHTML = '';
            }, 4000);
        }

        // Change hair color function
        function changeHairColor(color) {
            // Update hair color (head::before, head::after, pigtails)
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .character-head::before,
                .character-head::after,
                .character-pigtail,
                .character-ponytail,
                .character-short-hair,
                .character-long-hair {
                    background-color: ${color} !important;
                }
            `;

            // Remove any previous hair color style
            const previousStyle = document.getElementById('hair-color-style');
            if (previousStyle) {
                previousStyle.remove();
            }

            styleElement.id = 'hair-color-style';
            document.head.appendChild(styleElement);
        }

        // Change hair style function
        function changeHairStyle(style) {
            // Remove ALL current hair elements
            const pigtails = document.querySelectorAll('.character-pigtail');
            pigtails.forEach(el => el.remove());

            const ponytail = document.querySelector('.character-ponytail');
            if (ponytail) ponytail.remove();

            const shortHair = document.querySelector('.character-short-hair');
            if (shortHair) shortHair.remove();

            const longHair = document.querySelector('.character-long-hair');
            if (longHair) longHair.remove();

            const curlyHair = document.querySelector('.character-curly-hair');
            if (curlyHair) curlyHair.remove();

            const wavyHair = document.querySelector('.character-wavy-hair');
            if (wavyHair) wavyHair.remove();

            const bunHair = document.querySelector('.character-bun-hair');
            if (bunHair) bunHair.remove();

            const braids = document.querySelectorAll('.character-braid');
            braids.forEach(el => el.remove());

            // Get current hair color
            let hairColor = '#8B4513'; // Default brown
            const hairColorStyle = document.getElementById('hair-color-style');
            if (hairColorStyle) {
                const match = hairColorStyle.textContent.match(/background-color: (.*?) !important/);
                if (match && match[1]) {
                    hairColor = match[1];
                }
            }

            // Create new hair elements based on style
            if (style === 'pigtails') {
                // Create left pigtail
                const leftPigtail = document.createElement('div');
                leftPigtail.className = 'character-pigtail left';
                leftPigtail.style.backgroundColor = hairColor;
                character.appendChild(leftPigtail);

                // Create right pigtail
                const rightPigtail = document.createElement('div');
                rightPigtail.className = 'character-pigtail right';
                rightPigtail.style.backgroundColor = hairColor;
                character.appendChild(rightPigtail);

                // Update bangs to be shorter
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);

            } else if (style === 'ponytail') {
                const ponytail = document.createElement('div');
                ponytail.className = 'character-ponytail';
                ponytail.style.position = 'absolute';
                ponytail.style.width = '40px';
                ponytail.style.height = '80px';
                ponytail.style.backgroundColor = hairColor;
                ponytail.style.border = '3px solid #000';
                ponytail.style.borderRadius = '20px';
                ponytail.style.top = '0';
                ponytail.style.left = '50%';
                ponytail.style.transform = 'translateX(-50%)';
                ponytail.style.zIndex = '-1';

                character.appendChild(ponytail);

                // Update bangs to be shorter
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 70px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);

            } else if (style === 'short') {
                const shortHair = document.createElement('div');
                shortHair.className = 'character-short-hair';
                shortHair.style.position = 'absolute';
                shortHair.style.width = '110px';
                shortHair.style.height = '40px';
                shortHair.style.backgroundColor = hairColor;
                shortHair.style.border = '3px solid #000';
                shortHair.style.borderRadius = '50% 50% 10px 10px';
                shortHair.style.top = '-5px';
                shortHair.style.left = '50%';
                shortHair.style.transform = 'translateX(-50%)';
                shortHair.style.zIndex = '-1';

                character.appendChild(shortHair);

                // Update bangs to be shorter
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 10px !important;
                        top: 5px !important;
                        width: 60px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);

            } else if (style === 'long') {
                const longHair = document.createElement('div');
                longHair.className = 'character-long-hair';
                longHair.style.position = 'absolute';
                longHair.style.width = '120px';
                longHair.style.height = '150px';
                longHair.style.backgroundColor = hairColor;
                longHair.style.border = '3px solid #000';
                longHair.style.borderRadius = '50% 50% 40% 40%';
                longHair.style.top = '-10px';
                longHair.style.left = '50%';
                longHair.style.transform = 'translateX(-50%)';
                longHair.style.zIndex = '-1';

                character.appendChild(longHair);

                // Update bangs to be shorter
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 80px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);
            } else if (style === 'curly') {
                const curlyHair = document.createElement('div');
                curlyHair.className = 'character-curly-hair';
                curlyHair.style.position = 'absolute';
                curlyHair.style.width = '120px';
                curlyHair.style.height = '120px';
                curlyHair.style.backgroundColor = hairColor;
                curlyHair.style.border = '3px solid #000';
                curlyHair.style.borderRadius = '50%';
                curlyHair.style.top = '-10px';
                curlyHair.style.left = '50%';
                curlyHair.style.transform = 'translateX(-50%)';
                curlyHair.style.zIndex = '-1';

                // Create curly texture with multiple divs
                for (let i = 0; i < 12; i++) {
                    const curl = document.createElement('div');
                    curl.style.position = 'absolute';
                    curl.style.width = '20px';
                    curl.style.height = '20px';
                    curl.style.backgroundColor = hairColor;
                    curl.style.border = '2px solid #000';
                    curl.style.borderRadius = '50%';

                    // Position curls around the head
                    const angle = (i / 12) * Math.PI * 2;
                    const radius = 55;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;

                    curl.style.left = `calc(50% + ${x}px)`;
                    curl.style.top = `calc(50% + ${y}px)`;
                    curl.style.transform = 'translate(-50%, -50%)';

                    curlyHair.appendChild(curl);
                }

                character.appendChild(curlyHair);

                // Update bangs to be curly
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 70px !important;
                        border-radius: 40% 40% 60% 60% !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);
            } else if (style === 'wavy') {
                const wavyHair = document.createElement('div');
                wavyHair.className = 'character-wavy-hair';
                wavyHair.style.position = 'absolute';
                wavyHair.style.width = '120px';
                wavyHair.style.height = '140px';
                wavyHair.style.backgroundColor = hairColor;
                wavyHair.style.border = '3px solid #000';
                wavyHair.style.borderRadius = '50% 50% 30% 30%';
                wavyHair.style.top = '-10px';
                wavyHair.style.left = '50%';
                wavyHair.style.transform = 'translateX(-50%)';
                wavyHair.style.zIndex = '-1';

                // Create wavy texture with multiple wave layers
                for (let i = 0; i < 5; i++) {
                    const waveLayer = document.createElement('div');
                    waveLayer.style.position = 'absolute';
                    waveLayer.style.bottom = `${i * 20}px`;
                    waveLayer.style.left = '0';
                    waveLayer.style.width = '100%';
                    waveLayer.style.height = '20px';
                    waveLayer.style.borderBottom = '3px solid #000';
                    waveLayer.style.borderRadius = '50% 50% 0 0';

                    // Alternate the wave direction
                    if (i % 2 === 0) {
                        waveLayer.style.borderRadius = '0 0 50% 50%';
                    }

                    wavyHair.appendChild(waveLayer);
                }

                // Add side waves for more volume
                const leftWave = document.createElement('div');
                leftWave.style.position = 'absolute';
                leftWave.style.width = '30px';
                leftWave.style.height = '80px';
                leftWave.style.backgroundColor = hairColor;
                leftWave.style.border = '3px solid #000';
                leftWave.style.borderRadius = '50% 0 0 50%';
                leftWave.style.top = '20px';
                leftWave.style.left = '-15px';

                const rightWave = document.createElement('div');
                rightWave.style.position = 'absolute';
                rightWave.style.width = '30px';
                rightWave.style.height = '80px';
                rightWave.style.backgroundColor = hairColor;
                rightWave.style.border = '3px solid #000';
                rightWave.style.borderRadius = '0 50% 50% 0';
                rightWave.style.top = '20px';
                rightWave.style.right = '-15px';

                wavyHair.appendChild(leftWave);
                wavyHair.appendChild(rightWave);

                character.appendChild(wavyHair);

                // Update bangs to be wavy
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 75px !important;
                        border-radius: 40% 40% 60% 60% !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);
            } else if (style === 'bun') {
                // Create main bun
                const bunHair = document.createElement('div');
                bunHair.className = 'character-bun-hair';
                bunHair.style.position = 'absolute';
                bunHair.style.width = '50px';
                bunHair.style.height = '50px';
                bunHair.style.backgroundColor = hairColor;
                bunHair.style.border = '3px solid #000';
                bunHair.style.borderRadius = '50%';
                bunHair.style.top = '-35px';
                bunHair.style.left = '50%';
                bunHair.style.transform = 'translateX(-50%)';
                bunHair.style.zIndex = '-1';

                // Add texture to bun
                const bunTexture = document.createElement('div');
                bunTexture.style.position = 'absolute';
                bunTexture.style.width = '30px';
                bunTexture.style.height = '30px';
                bunTexture.style.top = '50%';
                bunTexture.style.left = '50%';
                bunTexture.style.transform = 'translate(-50%, -50%)';
                bunTexture.style.borderRadius = '50%';
                bunTexture.style.border = '2px solid #000';
                bunHair.appendChild(bunTexture);

                // Add hair band
                const hairBand = document.createElement('div');
                hairBand.style.position = 'absolute';
                hairBand.style.width = '54px';
                hairBand.style.height = '10px';
                hairBand.style.backgroundColor = '#FF6B8B';
                hairBand.style.border = '2px solid #000';
                hairBand.style.borderRadius = '5px';
                hairBand.style.bottom = '-5px';
                hairBand.style.left = '50%';
                hairBand.style.transform = 'translateX(-50%)';
                hairBand.style.zIndex = '1';
                bunHair.appendChild(hairBand);

                // Add side hair
                const leftSideHair = document.createElement('div');
                leftSideHair.style.position = 'absolute';
                leftSideHair.style.width = '20px';
                leftSideHair.style.height = '40px';
                leftSideHair.style.backgroundColor = hairColor;
                leftSideHair.style.border = '3px solid #000';
                leftSideHair.style.borderRadius = '10px 0 0 10px';
                leftSideHair.style.top = '10px';
                leftSideHair.style.left = '-10px';
                leftSideHair.style.zIndex = '-1';

                const rightSideHair = document.createElement('div');
                rightSideHair.style.position = 'absolute';
                rightSideHair.style.width = '20px';
                rightSideHair.style.height = '40px';
                rightSideHair.style.backgroundColor = hairColor;
                rightSideHair.style.border = '3px solid #000';
                rightSideHair.style.borderRadius = '0 10px 10px 0';
                rightSideHair.style.top = '10px';
                rightSideHair.style.right = '-10px';
                rightSideHair.style.zIndex = '-1';

                character.appendChild(bunHair);
                character.appendChild(leftSideHair);
                character.appendChild(rightSideHair);

                // Update bangs
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 70px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);
            } else if (style === 'braids') {
                // Create back hair
                const backHair = document.createElement('div');
                backHair.className = 'character-back-hair';
                backHair.style.position = 'absolute';
                backHair.style.width = '100px';
                backHair.style.height = '40px';
                backHair.style.backgroundColor = hairColor;
                backHair.style.border = '3px solid #000';
                backHair.style.borderRadius = '50% 50% 0 0';
                backHair.style.top = '-5px';
                backHair.style.left = '50%';
                backHair.style.transform = 'translateX(-50%)';
                backHair.style.zIndex = '-1';

                // Left braid
                const leftBraid = document.createElement('div');
                leftBraid.className = 'character-braid left';
                leftBraid.style.position = 'absolute';
                leftBraid.style.width = '25px';
                leftBraid.style.height = '100px';
                leftBraid.style.backgroundColor = hairColor;
                leftBraid.style.border = '3px solid #000';
                leftBraid.style.borderRadius = '10px';
                leftBraid.style.top = '10px';
                leftBraid.style.left = '-20px';
                leftBraid.style.zIndex = '-1';
                leftBraid.style.transform = 'rotate(5deg)';

                // Add braid texture - zigzag pattern
                for (let i = 1; i <= 8; i++) {
                    const leftSection = document.createElement('div');
                    leftSection.style.position = 'absolute';
                    leftSection.style.width = '50%';
                    leftSection.style.height = '10px';
                    leftSection.style.backgroundColor = hairColor;
                    leftSection.style.borderRight = '2px solid #000';
                    leftSection.style.top = (i * 12) + 'px';
                    leftSection.style.left = '0';

                    const rightSection = document.createElement('div');
                    rightSection.style.position = 'absolute';
                    rightSection.style.width = '50%';
                    rightSection.style.height = '10px';
                    rightSection.style.backgroundColor = hairColor;
                    rightSection.style.borderLeft = '2px solid #000';
                    rightSection.style.top = (i * 12) + 'px';
                    rightSection.style.right = '0';

                    // Add horizontal lines
                    const line = document.createElement('div');
                    line.style.position = 'absolute';
                    line.style.width = '100%';
                    line.style.height = '2px';
                    line.style.backgroundColor = '#000';
                    line.style.top = (i * 12) + 'px';

                    leftBraid.appendChild(leftSection);
                    leftBraid.appendChild(rightSection);
                    leftBraid.appendChild(line);
                }

                // Add hair tie at the bottom
                const leftTie = document.createElement('div');
                leftTie.style.position = 'absolute';
                leftTie.style.width = '25px';
                leftTie.style.height = '8px';
                leftTie.style.backgroundColor = '#FF6B8B';
                leftTie.style.border = '2px solid #000';
                leftTie.style.borderRadius = '5px';
                leftTie.style.bottom = '-4px';
                leftTie.style.left = '0';
                leftBraid.appendChild(leftTie);

                // Right braid - mirror of left
                const rightBraid = document.createElement('div');
                rightBraid.className = 'character-braid right';
                rightBraid.style.position = 'absolute';
                rightBraid.style.width = '25px';
                rightBraid.style.height = '100px';
                rightBraid.style.backgroundColor = hairColor;
                rightBraid.style.border = '3px solid #000';
                rightBraid.style.borderRadius = '10px';
                rightBraid.style.top = '10px';
                rightBraid.style.right = '-20px';
                rightBraid.style.zIndex = '-1';
                rightBraid.style.transform = 'rotate(-5deg)';

                // Add braid texture - zigzag pattern (same as left)
                for (let i = 1; i <= 8; i++) {
                    const leftSection = document.createElement('div');
                    leftSection.style.position = 'absolute';
                    leftSection.style.width = '50%';
                    leftSection.style.height = '10px';
                    leftSection.style.backgroundColor = hairColor;
                    leftSection.style.borderRight = '2px solid #000';
                    leftSection.style.top = (i * 12) + 'px';
                    leftSection.style.left = '0';

                    const rightSection = document.createElement('div');
                    rightSection.style.position = 'absolute';
                    rightSection.style.width = '50%';
                    rightSection.style.height = '10px';
                    rightSection.style.backgroundColor = hairColor;
                    rightSection.style.borderLeft = '2px solid #000';
                    rightSection.style.top = (i * 12) + 'px';
                    rightSection.style.right = '0';

                    // Add horizontal lines
                    const line = document.createElement('div');
                    line.style.position = 'absolute';
                    line.style.width = '100%';
                    line.style.height = '2px';
                    line.style.backgroundColor = '#000';
                    line.style.top = (i * 12) + 'px';

                    rightBraid.appendChild(leftSection);
                    rightBraid.appendChild(rightSection);
                    rightBraid.appendChild(line);
                }

                // Add hair tie at the bottom
                const rightTie = document.createElement('div');
                rightTie.style.position = 'absolute';
                rightTie.style.width = '25px';
                rightTie.style.height = '8px';
                rightTie.style.backgroundColor = '#FF6B8B';
                rightTie.style.border = '2px solid #000';
                rightTie.style.borderRadius = '5px';
                rightTie.style.bottom = '-4px';
                rightTie.style.left = '0';
                rightBraid.appendChild(rightTie);

                character.appendChild(backHair);
                character.appendChild(leftBraid);
                character.appendChild(rightBraid);

                // Update bangs
                const bangsStyle = document.createElement('style');
                bangsStyle.textContent = `
                    .character-head::after {
                        height: 15px !important;
                        top: 0px !important;
                        width: 70px !important;
                    }
                `;

                // Remove any previous bangs style
                const previousBangsStyle = document.getElementById('bangs-style');
                if (previousBangsStyle) {
                    previousBangsStyle.remove();
                }

                bangsStyle.id = 'bangs-style';
                document.head.appendChild(bangsStyle);
            }

            // Update dark mode styles
            const darkModeStyle = document.createElement('style');
            darkModeStyle.textContent = `
                .dark .character-pigtail,
                .dark .character-ponytail,
                .dark .character-short-hair,
                .dark .character-long-hair,
                .dark .character-curly-hair,
                .dark .character-wavy-hair,
                .dark .character-bun-hair,
                .dark .character-braid {
                    border: 3px solid #000 !important;
                }
            `;

            // Remove any previous dark mode style
            const previousDarkStyle = document.getElementById('hair-dark-style');
            if (previousDarkStyle) {
                previousDarkStyle.remove();
            }

            darkModeStyle.id = 'hair-dark-style';
            document.head.appendChild(darkModeStyle);
        }

        // Change outfit style function
        function changeOutfitStyle(style) {
            // Get current dress color
            let dressColor = '#A2D2FF'; // Default blue
            const dressColorStyle = document.getElementById('dress-color-style');
            if (dressColorStyle) {
                const match = dressColorStyle.textContent.match(/background-color: (.*?) !important/);
                if (match && match[1]) {
                    dressColor = match[1];
                }
            }

            const styleElement = document.createElement('style');

            if (style === 'dress') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px 20px 60px 60px !important;
                    }
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        bottom: 10px !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background: repeating-linear-gradient(
                            90deg,
                            transparent,
                            transparent 10px,
                            #FFD1DC 10px,
                            #FFD1DC 20px
                        ) !important;
                    }
                    .dark .character-body::after {
                        background: repeating-linear-gradient(
                            90deg,
                            transparent,
                            transparent 10px,
                            #FFD1DC 10px,
                            #FFD1DC 20px
                        ) !important;
                    }
                `;
            } else if (style === 'uniform') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px !important;
                    }
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        top: 30px !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background-color: white !important;
                        border-top: 2px solid #000 !important;
                        border-bottom: 2px solid #000 !important;
                    }
                    .dark .character-body::after {
                        background-color: white !important;
                        border-top: 2px solid #000 !important;
                        border-bottom: 2px solid #000 !important;
                    }
                `;
            } else if (style === 'casual') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px !important;
                        background: linear-gradient(
                            to bottom,
                            ${dressColor} 50%,
                            #444 50%
                        ) !important;
                    }
                    .dark .character-body {
                        border-radius: 20px !important;
                        background: linear-gradient(
                            to bottom,
                            ${dressColor} 50%,
                            #444 50%
                        ) !important;
                    }
                    .character-body::after {
                        content: none !important;
                    }
                `;
            } else if (style === 'princess') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px 20px 80px 80px !important;
                    }
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        bottom: 0 !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 40px !important;
                        background: radial-gradient(
                            circle at 50% 0,
                            transparent 20px,
                            gold 20px,
                            gold 25px,
                            transparent 25px
                        ) !important;
                        background-size: 40px 40px !important;
                        background-position: 0 0 !important;
                    }
                    .dark .character-body::after {
                        background: radial-gradient(
                            circle at 50% 0,
                            transparent 20px,
                            gold 20px,
                            gold 25px,
                            transparent 25px
                        ) !important;
                        background-size: 40px 40px !important;
                        background-position: 0 0 !important;
                    }
                `;
            } else if (style === 'swimsuit') {
                // Simple, clean swimsuit implementation
                styleElement.textContent = `
                    /* Body styling for swimsuit */
                    .character-body {
                        background-color: #FFD1DC !important; /* Skin color */
                        border-radius: 20px 20px 40% 40% !important; /* More oval shape at bottom */
                    }
                    .dark .character-body {
                        background-color: #FFD1DC !important;
                    }

                    /* Top swimsuit band */
                    .character-body::before {
                        content: "" !important;
                        position: absolute !important;
                        top: 15px !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background-color: ${dressColor} !important;
                        border-top: 2px solid #000 !important;
                        border-bottom: 2px solid #000 !important;
                    }

                    /* Bottom swimsuit band */
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        bottom: 0 !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background-color: ${dressColor} !important;
                        border-top: 2px solid #000 !important;
                        border-radius: 0 0 20px 20px !important;
                    }

                    /* Fix leg positioning for all screen sizes */
                    @media (max-width: 768px) {
                        .character-leg {
                            top: 135px !important;
                        }
                    }

                    @media (max-width: 480px) {
                        .character-leg {
                            top: 125px !important;
                        }
                    }
                `;

                // Add belly button
                setTimeout(() => {
                    // Create and add belly button
                    const bellyButton = document.createElement('div');
                    bellyButton.style.position = 'absolute';
                    bellyButton.style.width = '4px';
                    bellyButton.style.height = '4px';
                    bellyButton.style.backgroundColor = '#000';
                    bellyButton.style.borderRadius = '50%';
                    bellyButton.style.top = '50%';
                    bellyButton.style.left = '50%';
                    bellyButton.style.transform = 'translate(-50%, -50%)';
                    bellyButton.style.zIndex = '2';

                    const characterBody = character.querySelector('.character-body');
                    if (characterBody) {
                        // Remove any existing belly button first
                        const existingBellyButton = characterBody.querySelector('.belly-button');
                        if (existingBellyButton) {
                            existingBellyButton.remove();
                        }
                        characterBody.appendChild(bellyButton);
                    }
                }, 50);
            } else if (style === 'party') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px 20px 80px 80px !important;
                    }
                    .character-body::before {
                        content: "" !important;
                        position: absolute !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                        background-image: radial-gradient(circle, rgba(255,255,255,0.7) 2px, transparent 2px) !important;
                        background-size: 15px 15px !important;
                        z-index: 1 !important;
                    }
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        bottom: 0 !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background: repeating-linear-gradient(
                            90deg,
                            transparent,
                            transparent 5px,
                            gold 5px,
                            gold 10px
                        ) !important;
                    }
                    .dark .character-body::after {
                        background: repeating-linear-gradient(
                            90deg,
                            transparent,
                            transparent 5px,
                            gold 5px,
                            gold 10px
                        ) !important;
                    }
                `;
            } else if (style === 'pajamas') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px !important;
                        background: repeating-linear-gradient(
                            45deg,
                            ${dressColor},
                            ${dressColor} 10px,
                            rgba(255,255,255,0.5) 10px,
                            rgba(255,255,255,0.5) 20px
                        ) !important;
                    }
                    .dark .character-body {
                        border-radius: 20px !important;
                        background: repeating-linear-gradient(
                            45deg,
                            ${dressColor},
                            ${dressColor} 10px,
                            rgba(255,255,255,0.5) 10px,
                            rgba(255,255,255,0.5) 20px
                        ) !important;
                    }
                    .character-body::after {
                        content: none !important;
                    }
                `;
            } else if (style === 'sporty') {
                styleElement.textContent = `
                    .character-body {
                        border-radius: 20px !important;
                        background: ${dressColor} !important;
                    }
                    .dark .character-body {
                        background: ${dressColor} !important;
                    }
                    .character-body::after {
                        content: "" !important;
                        position: absolute !important;
                        top: 20px !important;
                        left: 0 !important;
                        width: 100% !important;
                        height: 20px !important;
                        background-color: white !important;
                        border-top: 2px solid #000 !important;
                        border-bottom: 2px solid #000 !important;
                    }
                    .dark .character-body::after {
                        background-color: white !important;
                        border-top: 2px solid #000 !important;
                        border-bottom: 2px solid #000 !important;
                    }
                    .character-body::before {
                        content: "✓" !important;
                        position: absolute !important;
                        top: 20px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        font-size: 20px !important;
                        color: #000 !important;
                        z-index: 2 !important;
                    }
                `;
            }

            // Remove any previous outfit style
            const previousStyle = document.getElementById('outfit-style');
            if (previousStyle) {
                previousStyle.remove();
            }

            styleElement.id = 'outfit-style';
            document.head.appendChild(styleElement);
        }

        // Change dress color function
        function changeDressColor(color) {
            // Update dress color
            const styleElement = document.createElement('style');
            styleElement.textContent = `
                .character-body {
                    background-color: ${color} !important;
                }
                .dark .character-body {
                    background-color: ${color} !important;
                }
            `;

            // Remove any previous dress color style
            const previousStyle = document.getElementById('dress-color-style');
            if (previousStyle) {
                previousStyle.remove();
            }

            styleElement.id = 'dress-color-style';
            document.head.appendChild(styleElement);
        }

        // Change expression function
        function changeExpression(expression) {
            // Remove current expression
            character.classList.remove('happy', 'sad', 'surprised', 'waving', 'excited', 'sleepy', 'angry', 'silly');

            // Add new expression
            character.classList.add(expression);
            currentExpression = expression;

            // Update speech bubble based on expression
            let message = '';
            if (expression === 'happy') {
                message = 'I\'m so happy today!';
            } else if (expression === 'sad') {
                message = 'I\'m feeling a bit sad...';
            } else if (expression === 'surprised') {
                message = 'Wow! That\'s surprising!';
            } else if (expression === 'waving') {
                message = 'Hello there! Nice to meet you!';
            } else if (expression === 'excited') {
                message = 'I\'m so excited! This is amazing!';
            } else if (expression === 'sleepy') {
                message = 'I\'m feeling a bit sleepy... *yawn*';
            } else if (expression === 'angry') {
                message = 'Hmph! I\'m a bit upset right now!';
            } else if (expression === 'silly') {
                message = 'Hehe! I\'m feeling silly today!';
            }

            speechBubble.querySelector('p').textContent = message;
            speechBubble.style.animation = 'none';
            void speechBubble.offsetWidth; // Trigger reflow
            speechBubble.style.animation = 'fadeInUp 0.5s ease forwards';
        }

        // Change head accessory function
        function changeHeadAccessory(accessory) {
            // Remove current head accessory if exists
            if (headAccessoryElement) {
                headAccessoryElement.remove();
                headAccessoryElement = null;
            }

            currentHeadAccessory = accessory;

            if (accessory === 'none') {
                return;
            }

            // Create new accessory element
            headAccessoryElement = document.createElement('div');
            headAccessoryElement.className = 'character-accessory head-' + accessory;

            if (accessory === 'bow') {
                headAccessoryElement.innerHTML = '🎀';
                headAccessoryElement.style.position = 'absolute';
                headAccessoryElement.style.top = '-15px';
                headAccessoryElement.style.left = '50%';
                headAccessoryElement.style.transform = 'translateX(-50%)';
                headAccessoryElement.style.fontSize = '30px';
                headAccessoryElement.style.zIndex = '5';
            } else if (accessory === 'hat') {
                headAccessoryElement.innerHTML = '👒';
                headAccessoryElement.style.position = 'absolute';
                headAccessoryElement.style.top = '-25px';
                headAccessoryElement.style.left = '50%';
                headAccessoryElement.style.transform = 'translateX(-50%)';
                headAccessoryElement.style.fontSize = '40px';
                headAccessoryElement.style.zIndex = '5';
            } else if (accessory === 'crown') {
                headAccessoryElement.innerHTML = '👑';
                headAccessoryElement.style.position = 'absolute';
                headAccessoryElement.style.top = '-25px';
                headAccessoryElement.style.left = '50%';
                headAccessoryElement.style.transform = 'translateX(-50%)';
                headAccessoryElement.style.fontSize = '35px';
                headAccessoryElement.style.zIndex = '5';
            } else if (accessory === 'flower') {
                headAccessoryElement.innerHTML = '🌸';
                headAccessoryElement.style.position = 'absolute';
                headAccessoryElement.style.top = '-10px';
                headAccessoryElement.style.right = '10px';
                headAccessoryElement.style.fontSize = '30px';
                headAccessoryElement.style.zIndex = '5';
            } else if (accessory === 'headband') {
                headAccessoryElement.innerHTML = '📿';
                headAccessoryElement.style.position = 'absolute';
                headAccessoryElement.style.top = '-5px';
                headAccessoryElement.style.left = '50%';
                headAccessoryElement.style.transform = 'translateX(-50%) rotate(90deg)';
                headAccessoryElement.style.fontSize = '30px';
                headAccessoryElement.style.zIndex = '5';
            }

            // Add accessory to character
            character.querySelector('.character-head').appendChild(headAccessoryElement);
        }

        // Change face accessory function
        function changeFaceAccessory(accessory) {
            // Remove current face accessory if exists
            if (faceAccessoryElement) {
                faceAccessoryElement.remove();
                faceAccessoryElement = null;
            }

            currentFaceAccessory = accessory;

            if (accessory === 'none') {
                return;
            }

            // Create new accessory element
            faceAccessoryElement = document.createElement('div');
            faceAccessoryElement.className = 'character-accessory face-' + accessory;

            if (accessory === 'glasses') {
                faceAccessoryElement.innerHTML = '👓';
                faceAccessoryElement.style.position = 'absolute';
                faceAccessoryElement.style.top = '40px';
                faceAccessoryElement.style.left = '50%';
                faceAccessoryElement.style.transform = 'translateX(-50%) scale(0.8)';
                faceAccessoryElement.style.fontSize = '30px';
                faceAccessoryElement.style.zIndex = '10';
            } else if (accessory === 'sunglasses') {
                faceAccessoryElement.innerHTML = '🕶️';
                faceAccessoryElement.style.position = 'absolute';
                faceAccessoryElement.style.top = '40px';
                faceAccessoryElement.style.left = '50%';
                faceAccessoryElement.style.transform = 'translateX(-50%) scale(0.8)';
                faceAccessoryElement.style.fontSize = '30px';
                faceAccessoryElement.style.zIndex = '10';
            } else if (accessory === 'mask') {
                faceAccessoryElement.innerHTML = '😷';
                faceAccessoryElement.style.position = 'absolute';
                faceAccessoryElement.style.top = '40px';
                faceAccessoryElement.style.left = '50%';
                faceAccessoryElement.style.transform = 'translateX(-50%) scale(0.8)';
                faceAccessoryElement.style.fontSize = '30px';
                faceAccessoryElement.style.zIndex = '10';
            }

            // Add accessory to character
            character.querySelector('.character-head').appendChild(faceAccessoryElement);
        }

        // Change neck accessory function
        function changeNeckAccessory(accessory) {
            // Remove current neck accessory if exists
            if (neckAccessoryElement) {
                neckAccessoryElement.remove();
                neckAccessoryElement = null;
            }

            currentNeckAccessory = accessory;

            if (accessory === 'none') {
                return;
            }

            // Create new accessory element
            neckAccessoryElement = document.createElement('div');
            neckAccessoryElement.className = 'character-accessory neck-' + accessory;

            if (accessory === 'necklace') {
                neckAccessoryElement.innerHTML = '📿';
                neckAccessoryElement.style.position = 'absolute';
                neckAccessoryElement.style.top = '75px';
                neckAccessoryElement.style.left = '50%';
                neckAccessoryElement.style.transform = 'translateX(-50%) scale(0.7)';
                neckAccessoryElement.style.fontSize = '30px';
                neckAccessoryElement.style.zIndex = '6';
            } else if (accessory === 'scarf') {
                neckAccessoryElement.innerHTML = '🧣';
                neckAccessoryElement.style.position = 'absolute';
                neckAccessoryElement.style.top = '75px';
                neckAccessoryElement.style.left = '50%';
                neckAccessoryElement.style.transform = 'translateX(-50%) scale(0.8)';
                neckAccessoryElement.style.fontSize = '30px';
                neckAccessoryElement.style.zIndex = '6';
            } else if (accessory === 'bowtie') {
                neckAccessoryElement.innerHTML = '🎀';
                neckAccessoryElement.style.position = 'absolute';
                neckAccessoryElement.style.top = '75px';
                neckAccessoryElement.style.left = '50%';
                neckAccessoryElement.style.transform = 'translateX(-50%) scale(0.7)';
                neckAccessoryElement.style.fontSize = '25px';
                neckAccessoryElement.style.zIndex = '6';
            }

            // Add accessory to character
            character.appendChild(neckAccessoryElement);
        }

        // Change hand accessory function
        function changeHandAccessory(accessory) {
            // Remove current hand accessory if exists
            if (handAccessoryElement) {
                handAccessoryElement.remove();
                handAccessoryElement = null;
            }

            currentHandAccessory = accessory;

            if (accessory === 'none') {
                return;
            }

            // Create new accessory element
            handAccessoryElement = document.createElement('div');
            handAccessoryElement.className = 'character-accessory hand-' + accessory;

            if (accessory === 'bracelet') {
                handAccessoryElement.innerHTML = '💎';
                handAccessoryElement.style.position = 'absolute';
                handAccessoryElement.style.top = '120px';
                handAccessoryElement.style.right = '5px';
                handAccessoryElement.style.transform = 'scale(0.7)';
                handAccessoryElement.style.fontSize = '25px';
                handAccessoryElement.style.zIndex = '6';
            } else if (accessory === 'gloves') {
                handAccessoryElement.innerHTML = '🧤';
                handAccessoryElement.style.position = 'absolute';
                handAccessoryElement.style.top = '120px';
                handAccessoryElement.style.right = '5px';
                handAccessoryElement.style.transform = 'scale(0.7)';
                handAccessoryElement.style.fontSize = '25px';
                handAccessoryElement.style.zIndex = '6';
            } else if (accessory === 'ring') {
                handAccessoryElement.innerHTML = '💍';
                handAccessoryElement.style.position = 'absolute';
                handAccessoryElement.style.top = '120px';
                handAccessoryElement.style.right = '5px';
                handAccessoryElement.style.transform = 'scale(0.7)';
                handAccessoryElement.style.fontSize = '25px';
                handAccessoryElement.style.zIndex = '6';
            }

            // Add accessory to character
            character.appendChild(handAccessoryElement);
        }

        // Change footwear function
        function changeFootwear(style) {
            currentFootwear = style;

            const styleElement = document.createElement('style');

            if (style === 'boots') {
                styleElement.textContent = `
                    .character-leg::after {
                        height: 20px !important;
                        background-color: #8B4513 !important;
                    }
                    .dark .character-leg::after {
                        background-color: #8B4513 !important;
                    }
                `;
            } else if (style === 'sneakers') {
                styleElement.textContent = `
                    .character-leg::after {
                        height: 15px !important;
                        background-color: white !important;
                        border-color: #444 !important;
                    }
                    .dark .character-leg::after {
                        background-color: white !important;
                        border-color: #444 !important;
                    }
                `;
            } else if (style === 'sandals') {
                styleElement.textContent = `
                    .character-leg::after {
                        height: 10px !important;
                        background-color: #FFD700 !important;
                    }
                    .dark .character-leg::after {
                        background-color: #FFD700 !important;
                    }
                `;
            } else if (style === 'heels') {
                styleElement.textContent = `
                    .character-leg::after {
                        height: 15px !important;
                        background-color: #FF6B8B !important;
                        border-bottom-width: 5px !important;
                    }
                    .dark .character-leg::after {
                        background-color: #FF6B8B !important;
                        border-bottom-width: 5px !important;
                    }
                `;
            } else {
                // Default
                styleElement.textContent = `
                    .character-leg::after {
                        height: 15px !important;
                        background-color: #FF6B8B !important;
                    }
                    .dark .character-leg::after {
                        background-color: #FF6B8B !important;
                    }
                `;
            }

            // Remove any previous footwear style
            const previousStyle = document.getElementById('footwear-style');
            if (previousStyle) {
                previousStyle.remove();
            }

            styleElement.id = 'footwear-style';
            document.head.appendChild(styleElement);
        }

        // Change background function
        function changeBackground(bg) {
            currentBackground = bg;

            // Remove current background if exists
            if (backgroundElement) {
                backgroundElement.remove();
                backgroundElement = null;
            }

            if (bg === 'none') {
                return;
            }

            // Create background element
            backgroundElement = document.createElement('div');
            backgroundElement.className = 'character-background ' + bg;
            backgroundElement.style.position = 'absolute';
            backgroundElement.style.top = '0';
            backgroundElement.style.left = '0';
            backgroundElement.style.width = '100%';
            backgroundElement.style.height = '100%';
            backgroundElement.style.zIndex = '-5';
            backgroundElement.style.opacity = '0.5';
            backgroundElement.style.borderRadius = '10px';
            backgroundElement.style.pointerEvents = 'none';

            if (bg === 'beach') {
                backgroundElement.style.background = 'linear-gradient(to bottom, #87CEEB 60%, #F5DEB3 40%)';
            } else if (bg === 'park') {
                backgroundElement.style.background = 'linear-gradient(to bottom, #87CEEB 70%, #90EE90 30%)';
            } else if (bg === 'school') {
                backgroundElement.style.background = '#F5F5F5';
                backgroundElement.style.backgroundImage = 'repeating-linear-gradient(0deg, transparent, transparent 20px, #DDD 20px, #DDD 21px)';
            } else if (bg === 'party') {
                backgroundElement.style.background = '#FFD1DC';
                backgroundElement.style.backgroundImage = 'radial-gradient(circle, #FFD700 2px, transparent 2px)';
                backgroundElement.style.backgroundSize = '20px 20px';
            } else if (bg === 'space') {
                backgroundElement.style.background = '#000033';
                backgroundElement.style.backgroundImage = 'radial-gradient(white, rgba(255,255,255,.2) 2px, transparent 2px)';
                backgroundElement.style.backgroundSize = '50px 50px';
            }

            // Add background to character container
            character.parentElement.insertBefore(backgroundElement, character);
        }

        // Change prop function
        function changeProp(prop) {
            // Remove current prop if exists
            if (propElement) {
                propElement.remove();
                propElement = null;
            }

            currentProp = prop;

            if (prop === 'none') {
                return;
            }

            // Create prop element
            propElement = document.createElement('div');
            propElement.className = 'character-prop ' + prop;
            propElement.style.position = 'absolute';
            propElement.style.zIndex = '7';

            if (prop === 'balloon') {
                propElement.innerHTML = '🎈';
                propElement.style.top = '20px';
                propElement.style.left = '20px'; // Changed from right to left
                propElement.style.fontSize = '40px';
                propElement.style.transform = 'rotate(-10deg)'; // Changed rotation
            } else if (prop === 'book') {
                propElement.innerHTML = '📚';
                propElement.style.bottom = '50px';
                propElement.style.left = '20px';
                propElement.style.fontSize = '30px';
            } else if (prop === 'umbrella') {
                propElement.innerHTML = '☂️';
                propElement.style.top = '0';
                propElement.style.right = '20px';
                propElement.style.fontSize = '40px';
                propElement.style.transform = 'rotate(10deg)';
            } else if (prop === 'icecream') {
                propElement.innerHTML = '🍦';
                propElement.style.top = '80px';
                propElement.style.left = '10px'; // Changed from right to left
                propElement.style.fontSize = '30px';
            } else if (prop === 'gift') {
                propElement.innerHTML = '🎁';
                propElement.style.bottom = '40px';
                propElement.style.left = '20px';
                propElement.style.fontSize = '30px';
            }

            // Add prop to character container
            character.parentElement.appendChild(propElement);
        }

        // Tic-tac-toe game
        const playButton = document.getElementById('playButton');
        const gameContainer = document.getElementById('gameContainer');
        const gameBoard = document.getElementById('gameBoard');
        const gameStatus = document.getElementById('gameStatus');
        const resetButton = document.getElementById('resetButton');
        const cells = document.querySelectorAll('.game-cell');

        let currentPlayer = Math.random() < 0.5 ? 'x' : 'o'; // Randomly choose who goes first
        let gameState = ['', '', '', '', '', '', '', '', ''];
        let gameActive = true;

        const winningConditions = [
            [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
            [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
            [0, 4, 8], [2, 4, 6]             // diagonals
        ];

        playButton.addEventListener('click', function() {
            gameContainer.style.display = 'block';
            playButton.style.display = 'none';
            resetGame();
        });

        resetButton.addEventListener('click', resetGame);

        cells.forEach(cell => {
            cell.addEventListener('click', () => handleCellClick(cell));
        });

        function handleCellClick(cell) {
            const index = cell.getAttribute('data-index');

            if (gameState[index] !== '' || !gameActive) {
                return;
            }

            gameState[index] = currentPlayer;
            cell.innerHTML = '';
            cell.classList.add(currentPlayer);

            // Add face features
            if (currentPlayer === 'x') {
                // Add boy face features
                const mouth = document.createElement('div');
                mouth.className = 'mouth';
                cell.appendChild(mouth);
            } else {
                // Add girl face features
                const leftBlush = document.createElement('div');
                leftBlush.className = 'blush left';
                cell.appendChild(leftBlush);

                const rightBlush = document.createElement('div');
                rightBlush.className = 'blush right';
                cell.appendChild(rightBlush);

                const mouth = document.createElement('div');
                mouth.className = 'mouth';
                cell.appendChild(mouth);
            }

            if (checkWin()) {
                const winner = currentPlayer === 'x' ? 'Your' : 'Shreya';
                gameStatus.textContent = `${winner} wins!`;
                gameActive = false;
                return;
            }

            if (checkDraw()) {
                gameStatus.textContent = "It's a draw!";
                gameActive = false;
                return;
            }

            currentPlayer = currentPlayer === 'x' ? 'o' : 'x';
            const nextPlayer = currentPlayer === 'x' ? 'Your' : 'Shreya\'s';
            gameStatus.textContent = `${nextPlayer} turn`;

            // If playing against computer and it's O's turn
            if (currentPlayer === 'o' && gameActive) {
                setTimeout(computerMove, 500);
            }
        }

        function computerMove() {
            // Simple AI: first try to win, then block, then random move
            let index = findBestMove();
            gameState[index] = currentPlayer;

            const cell = document.querySelector(`.game-cell[data-index="${index}"]`);
            cell.innerHTML = '';
            cell.classList.add(currentPlayer);

            // Add girl face features
            const leftBlush = document.createElement('div');
            leftBlush.className = 'blush left';
            cell.appendChild(leftBlush);

            const rightBlush = document.createElement('div');
            rightBlush.className = 'blush right';
            cell.appendChild(rightBlush);

            const mouth = document.createElement('div');
            mouth.className = 'mouth';
            cell.appendChild(mouth);

            if (checkWin()) {
                const winner = currentPlayer === 'x' ? 'Your' : 'Shreya\'s';
                gameStatus.textContent = `${winner} wins!`;
                gameActive = false;
                return;
            }

            if (checkDraw()) {
                gameStatus.textContent = "It's a draw!";
                gameActive = false;
                return;
            }

            currentPlayer = 'x';
            gameStatus.textContent = `Your turn`;
        }

        function findBestMove() {
            // Try to win
            for (let i = 0; i < gameState.length; i++) {
                if (gameState[i] === '') {
                    gameState[i] = 'o';
                    if (checkWin()) {
                        gameState[i] = '';
                        return i;
                    }
                    gameState[i] = '';
                }
            }

            // Try to block
            for (let i = 0; i < gameState.length; i++) {
                if (gameState[i] === '') {
                    gameState[i] = 'x';
                    if (checkWin()) {
                        gameState[i] = '';
                        return i;
                    }
                    gameState[i] = '';
                }
            }

            // Try center
            if (gameState[4] === '') {
                return 4;
            }

            // Try corners
            const corners = [0, 2, 6, 8];
            const availableCorners = corners.filter(i => gameState[i] === '');
            if (availableCorners.length > 0) {
                return availableCorners[Math.floor(Math.random() * availableCorners.length)];
            }

            // Random move
            const availableMoves = gameState.map((val, idx) => val === '' ? idx : null).filter(val => val !== null);
            return availableMoves[Math.floor(Math.random() * availableMoves.length)];
        }

        function checkWin() {
            for (let i = 0; i < winningConditions.length; i++) {
                const [a, b, c] = winningConditions[i];
                if (gameState[a] && gameState[a] === gameState[b] && gameState[a] === gameState[c]) {
                    return true;
                }
            }
            return false;
        }

        function checkDraw() {
            return !gameState.includes('');
        }

        function resetGame() {
            currentPlayer = Math.random() < 0.5 ? 'x' : 'o'; // Randomly choose who goes first
            gameState = ['', '', '', '', '', '', '', '', ''];
            gameActive = true;
            const firstPlayer = currentPlayer === 'x' ? 'Your' : 'Shreya\'s';
            gameStatus.textContent = `${firstPlayer} turn`;

            cells.forEach(cell => {
                cell.innerHTML = '';
                cell.classList.remove('x', 'o');
            });

            // If computer goes first
            if (currentPlayer === 'o') {
                setTimeout(computerMove, 500);
            }
        }
    });
</script>
{{ end }}