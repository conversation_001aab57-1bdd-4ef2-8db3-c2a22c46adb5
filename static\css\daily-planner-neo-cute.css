/* Neo-brutalism Daily Planner Styles */

/* Daily planner container */
.planner-container {
    position: relative;
    padding: 40px 20px;
    overflow: hidden;
    background-color: #fffef0;
    border: 3px solid #000;
    border-radius: 0;
    box-shadow: 10px 10px 0 rgba(0, 0, 0, 0.7);
    margin: 40px 20px;
}

.dark .planner-container {
    background-color: #1a1a1a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 10px 10px 0 rgba(255, 255, 255, 0.15);
}

/* Cute background pattern */
.planner-container::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(#C7CEEA 6px, transparent 6px),
        radial-gradient(#C7CEEA 6px, transparent 6px);
    background-size: 30px 30px;
    background-position: 0 0, 15px 15px;
    opacity: 0.3;
    z-index: 0;
}

.dark .planner-container::before {
    background-image:
        radial-gradient(rgba(199, 206, 234, 0.3) 6px, transparent 6px),
        radial-gradient(rgba(199, 206, 234, 0.3) 6px, transparent 6px);
}

/* Planner title */
.planner-title {
    position: relative;
    font-weight: 700;
    color: #000;
    margin-bottom: 1.5rem;
    display: inline-block;
    padding: 10px 20px;
    background-color: #C7CEEA;
    border: 3px solid #000;
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    z-index: 1;
    font-family: 'Playfair Display', serif;
    letter-spacing: 1px;
}

.dark .planner-title {
    color: #fff;
    background-color: rgba(199, 206, 234, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Planner description */
.planner-description {
    position: relative;
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    z-index: 1;
}

.dark .planner-description {
    color: rgba(255, 255, 255, 0.7);
}

/* Planner layout */
.planner-layout {
    display: flex;
    gap: 20px;
    position: relative;
    z-index: 1;
}

@media (max-width: 991px) {
    .planner-layout {
        flex-direction: column;
    }
}

/* Task panel */
.task-panel {
    flex: 1;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 20px;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .task-panel {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Task panel header */
.task-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
}

.dark .task-panel-header {
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.task-panel-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
    color: #000;
}

.dark .task-panel-title {
    color: #fff;
}

/* Task form */
.task-form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.task-input {
    flex: 1;
    padding: 10px;
    border: 2px solid #000;
    border-radius: 0;
    font-size: 1rem;
    background-color: #fff;
}

.dark .task-input {
    border: 2px solid rgba(255, 255, 255, 0.7);
    background-color: #333;
    color: #fff;
}

.task-add {
    padding: 10px 15px;
    background-color: #B5EAD7;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .task-add {
    background-color: rgba(181, 234, 215, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.task-add:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .task-add:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Task items */
.task-items {
    min-height: 200px;
    padding: 10px;
    border: 2px dashed #000;
    background-color: #f8f8f8;
}

.dark .task-items {
    border: 2px dashed rgba(255, 255, 255, 0.7);
    background-color: #333;
}

.task-item {
    padding: 10px;
    margin-bottom: 10px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    cursor: move;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
}

.dark .task-item {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.task-item:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .task-item:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

.task-item-text {
    flex: 1;
    font-weight: 600;
}

.task-item-remove {
    background-color: transparent;
    border: none;
    color: #000;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0 5px;
}

.dark .task-item-remove {
    color: #fff;
}

/* Schedule panel */
.schedule-panel {
    flex: 2;
    background-color: #fff;
    border: 3px solid #000;
    border-radius: 0;
    padding: 20px;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .schedule-panel {
    background-color: #2a2a2a;
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

/* Schedule panel header */
.schedule-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #000;
    padding-bottom: 10px;
}

.dark .schedule-panel-header {
    border-bottom: 2px solid rgba(255, 255, 255, 0.7);
}

.schedule-panel-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0;
    color: #000;
}

.dark .schedule-panel-title {
    color: #fff;
}

.schedule-date {
    font-size: 1.1rem;
    font-weight: 600;
    color: #555;
}

.dark .schedule-date {
    color: rgba(255, 255, 255, 0.7);
}

/* Schedule grid */
.schedule-grid {
    display: grid;
    grid-template-columns: 80px 1fr;
    gap: 10px;
}

.schedule-hours {
    display: grid;
    grid-template-rows: repeat(12, 60px);
    gap: 10px;
}

.schedule-hour {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    color: #000;
}

.dark .schedule-hour {
    background-color: #333;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

.schedule-slots {
    display: grid;
    grid-template-rows: repeat(12, 60px);
    gap: 10px;
}

.schedule-slot {
    background-color: #f8f8f8;
    border: 2px dashed #000;
    border-radius: 0;
    padding: 5px;
    min-height: 60px;
}

.dark .schedule-slot {
    background-color: #333;
    border: 2px dashed rgba(255, 255, 255, 0.7);
}

.schedule-slot.highlight {
    background-color: #e0f7fa;
    border: 2px solid #00acc1;
}

.dark .schedule-slot.highlight {
    background-color: rgba(0, 172, 193, 0.2);
    border: 2px solid rgba(0, 172, 193, 0.7);
}

/* Scheduled task */
.scheduled-task {
    background-color: #A2D2FF;
    border: 2px solid #000;
    border-radius: 0;
    padding: 5px 10px;
    margin-bottom: 5px;
    cursor: move;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
    transition: all 0.3s ease;
    font-weight: 600;
}

.dark .scheduled-task {
    background-color: rgba(162, 210, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
    color: #fff;
}

.scheduled-task:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .scheduled-task:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Clear notice */
.clear-notice {
    margin-top: 20px;
    padding: 10px;
    background-color: #FFD1DC;
    border: 2px solid #000;
    border-radius: 0;
    text-align: center;
    font-size: 0.9rem;
    color: #000;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
}

.dark .clear-notice {
    background-color: rgba(255, 209, 220, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
}

/* Affirmations */
.affirmations {
    margin-top: 40px;
    padding: 20px;
    background-color: #B5EAD7;
    border: 3px solid #000;
    border-radius: 0;
    text-align: center;
    position: relative;
    z-index: 1;
    box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmations {
    background-color: rgba(181, 234, 215, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.7);
    box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-text {
    font-size: 1.3rem;
    font-weight: 700;
    color: #000;
    margin-bottom: 10px;
    font-style: italic;
}

.dark .affirmation-text {
    color: #fff;
}

.affirmation-button {
    padding: 8px 15px;
    background-color: #fff;
    border: 2px solid #000;
    border-radius: 0;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-button {
    background-color: #2a2a2a;
    border: 2px solid rgba(255, 255, 255, 0.7);
    color: #fff;
    box-shadow: 3px 3px 0 rgba(255, 255, 255, 0.15);
}

.affirmation-button:hover {
    transform: translate(-2px, -2px);
    box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
}

.dark .affirmation-button:hover {
    box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
}

/* Responsive design */
@media (max-width: 991px) {
    .planner-container {
        margin: 30px 15px;
        padding: 30px 15px;
        box-shadow: 8px 8px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .planner-container {
        box-shadow: 8px 8px 0 rgba(255, 255, 255, 0.15);
    }
    
    .planner-title {
        font-size: 1.8rem;
    }
    
    .task-panel, .schedule-panel {
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .task-panel, .dark .schedule-panel {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
    
    .affirmations {
        box-shadow: 5px 5px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .affirmations {
        box-shadow: 5px 5px 0 rgba(255, 255, 255, 0.15);
    }
}

@media (max-width: 768px) {
    .planner-container {
        margin: 20px 10px;
        padding: 20px 10px;
        box-shadow: 6px 6px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .planner-container {
        box-shadow: 6px 6px 0 rgba(255, 255, 255, 0.15);
    }
    
    .planner-title {
        font-size: 1.5rem;
        padding: 8px 15px;
    }
    
    .planner-description {
        font-size: 1rem;
    }
    
    .task-panel, .schedule-panel {
        padding: 15px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .task-panel, .dark .schedule-panel {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    .task-panel-title, .schedule-panel-title {
        font-size: 1.1rem;
    }
    
    .schedule-grid {
        grid-template-columns: 60px 1fr;
    }
    
    .schedule-hours, .schedule-slots {
        grid-template-rows: repeat(12, 50px);
    }
    
    .schedule-hour, .schedule-slot {
        min-height: 50px;
    }
    
    .affirmation-text {
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .planner-container {
        margin: 15px 5px;
        padding: 15px 5px;
        box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.7);
    }
    
    .dark .planner-container {
        box-shadow: 4px 4px 0 rgba(255, 255, 255, 0.15);
    }
    
    .planner-title {
        font-size: 1.3rem;
        padding: 6px 12px;
    }
    
    .task-form {
        flex-direction: column;
    }
    
    .schedule-grid {
        grid-template-columns: 50px 1fr;
    }
    
    .schedule-hours, .schedule-slots {
        grid-template-rows: repeat(12, 45px);
    }
    
    .schedule-hour, .schedule-slot {
        min-height: 45px;
    }
    
    .schedule-hour {
        font-size: 0.8rem;
    }
}
